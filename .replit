modules = ["go-1.21", "nodejs-20", "web", "postgresql-16"]
run = "node index.js"

[nix]
channel = "stable-24_05"
packages = ["redis"]

[deployment]
run = ["sh", "-c", "node index.js"]

[workflows]
runButton = "Build Frontend and Run Backend"

[[workflows.workflow]]
name = "Run Go Backend"
author = 43435734
mode = "sequential"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "cd web-dashboard/frontend && npm run build"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "cd web-dashboard/backend && go run main.go"

[[workflows.workflow]]
name = "Build Frontend and Run Backend"
author = 43435734
mode = "sequential"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "cd web-dashboard/frontend && npm run build"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "cd web-dashboard/backend && go run main.go"

[[ports]]
localPort = 5000
externalPort = 5000

[[ports]]
localPort = 6379


