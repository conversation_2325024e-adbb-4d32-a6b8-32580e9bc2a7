package database

import (
	"database/sql"
	"fmt"
	"net/url"
	"os"
	"path/filepath"
	"time"

	_ "github.com/jackc/pgx/v5/stdlib"
)

var DB *sql.DB

func InitDB() error {
	var err error
	var pgserver = os.Getenv("PGHOST")
	var pgdb = os.Getenv("PGDATABASE")
	var pgport = os.Getenv("PGPORT")
	var pguser = os.Getenv("PGUSER")
	var pgpassword = url.QueryEscape(os.Getenv("PGPASSWORD"))

	var connstring = fmt.Sprintf("postgres://%s:%s@%s:%s/%s?sslmode=require&default_query_exec_mode=simple_protocol", pguser, pgpassword, pgserver, pgport, pgdb)

	DB, err = sql.Open("pgx", connstring)
	if err != nil {
		return fmt.Errorf("failed to connect to database: %s", err)
	}
	// Connection pool settings optimized for pgx
	DB.SetMaxOpenConns(20)                  // Maximum number of open connections
	DB.SetMaxIdleConns(10)                  // Maximum number of idle connections
	DB.SetConnMaxLifetime(10 * time.Minute) // Maximum connection lifetime
	DB.SetConnMaxIdleTime(5 * time.Minute)  // Maximum idle time

	// Test the connection
	if err = DB.Ping(); err != nil {
		return fmt.Errorf("failed to ping database: %s", err)
	}

	// Initialize schema by running schema.sql
	if err = runSchemaFile(); err != nil {
		return fmt.Errorf("failed to initialize schema: %s", err)
	}

	return nil
}

func CloseDB() error {
	return DB.Close()
}

// GetRecords retrieves records from the database with a limit
func GetRecords(limit int) ([][]string, error) {
	// This is a placeholder implementation
	// You'll need to modify this based on your actual data structure
	query := `SELECT id::text, created_at::text, 'sample_value' as value FROM users LIMIT $1`

	rows, err := DB.Query(query, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to query records: %v", err)
	}
	defer rows.Close()

	// Initialize empty slice instead of nil
	records := make([][]string, 0)
	for rows.Next() {
		var id, date, value string
		if err := rows.Scan(&id, &date, &value); err != nil {
			return nil, fmt.Errorf("failed to scan row: %v", err)
		}
		records = append(records, []string{id, date, value})
	}

	return records, nil
}

func runSchemaFile() error {
	// Get the path to schema.sql
	schemaPath := os.Getenv("SCHEMA_PATH")
	if schemaPath == "" {
		schemaPath = filepath.Join("database", "schema.sql")
	}

	// Read the schema file
	schemaSQL, err := os.ReadFile(schemaPath)
	if err != nil {
		return fmt.Errorf("failed to read schema file: %s", err)
	}

	// Execute the schema SQL
	_, err = DB.Exec(string(schemaSQL))
	if err != nil {
		return fmt.Errorf("failed to execute schema: %s", err)
	}

	return nil
}

func RebuildSchema(schema string) error {
	query := fmt.Sprintf(`DROP SCHEMA IF EXISTS %s CASCADE;
			  CREATE SCHEMA %s;`, schema, schema)

	_, err := DB.Exec(query)
	if err != nil {
		return fmt.Errorf("failed to rebuild schema: %s", err)
	}

	return nil
}
