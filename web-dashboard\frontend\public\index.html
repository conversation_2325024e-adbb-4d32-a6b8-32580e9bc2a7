<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset='utf-8'>
	<meta name='viewport' content='width=device-width,initial-scale=1'>

	<title>Svelte app</title>

	<link rel='icon' type='image/png' href='/favicon.png'>
	<link rel='stylesheet' href='/global.css'>
	<link rel='stylesheet' href='/build/bundle.css'>

	<!-- Enterprise reCAPTCHA v3 Script - Load with site key from environment -->
	<script>
		// Global flag to track reCAPTCHA loading status
		window.recaptchaLoading = false;
		window.recaptchaLoaded = false;
		window.recaptchaSiteKey = null;

		// Load Enterprise reCAPTCHA script with site key
		window.loadRecaptcha = function() {
			if (window.recaptchaLoading || window.recaptchaLoaded) {
				console.log('reCAPTCHA already loading or loaded');
				return;
			}

			window.recaptchaLoading = true;
			console.log('Starting reCAPTCHA configuration fetch...');

			// First check if reCAPTCHA is enabled
			fetch('/api/system-settings/settings/recaptcha_enabled')
				.then(response => {
					if (!response.ok) {
						console.log('Could not fetch reCAPTCHA enabled setting, proceeding with reCAPTCHA load');
						return { value: true }; // Default to enabled
					}
					return response.json();
				})
				.then(data => {
					const isEnabled = data.value === true || data.value === 'true';
					if (!isEnabled) {
						console.log('reCAPTCHA is disabled in system settings, skipping load');
						window.recaptchaLoading = false;
						return Promise.reject('RECAPTCHA_DISABLED'); // Stop the chain
					}
					
					// Proceed with loading reCAPTCHA
					return fetch('/api/recaptcha/sitekey');
				})
				.then(response => {
					console.log('reCAPTCHA config response status:', response.status);
					if (!response.ok) {
						throw new Error(`HTTP ${response.status}: ${response.statusText}`);
					}
					return response.json();
				})
				.then(data => {
					console.log('reCAPTCHA config data:', data);
					if (data.siteKey) {
						window.recaptchaSiteKey = data.siteKey;
						const script = document.createElement('script');
						script.src = `https://www.google.com/recaptcha/enterprise.js?render=${data.siteKey}`;
						script.async = true;
						script.defer = true;
						
						script.onload = function() {
							console.log('Enterprise reCAPTCHA script loaded successfully');
							window.recaptchaLoaded = true;
							window.recaptchaLoading = false;
						};
						
						script.onerror = function(error) {
							console.error('Failed to load Enterprise reCAPTCHA script:', error);
							window.recaptchaLoading = false;
						};
						
						document.head.appendChild(script);
						console.log('Enterprise reCAPTCHA v3 script added to page with site key:', data.siteKey);
					} else {
						console.error('No site key received from server');
						window.recaptchaLoading = false;
					}
				})
				.catch(err => {
					if (err === 'RECAPTCHA_DISABLED') {
						console.log('reCAPTCHA loading skipped - disabled in settings');
					} else {
						console.error('Failed to load reCAPTCHA config:', err);
					}
					window.recaptchaLoading = false;
				});
		};

		// Load when DOM is ready
		if (document.readyState === 'loading') {
			document.addEventListener('DOMContentLoaded', window.loadRecaptcha);
		} else {
			window.loadRecaptcha();
		}
	</script>

	<script defer src='/build/bundle.js'></script>
</head>

<body>
</body>
</html>
