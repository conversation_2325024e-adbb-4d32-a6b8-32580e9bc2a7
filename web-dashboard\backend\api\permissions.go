package api

import (
	"encoding/json"
	"net/http"
	"strconv"
	"web-dashboard/backend/security"

	"github.com/gorilla/mux"
)

type CheckPermissionRequest struct {
	Resource string `json:"resource"`
	Action   string `json:"action"`
}

type CheckPermissionResponse struct {
	HasPermission bool `json:"has_permission"`
}

// AttachPermissionRoutes attaches all permission-related routes to the router
func AttachPermissionRoutes(router *mux.Router) {
	router.HandleFunc("/permissions", RequirePermissionManagement(getPermissionsHandler)).Methods("GET")
	router.HandleFunc("/permissions/{id:[0-9]+}", RequirePermissionManagement(getPermissionHandler)).Methods("GET")
}

func getPermissionsHandler(w http.ResponseWriter, r *http.Request) {
	permissions, err := security.GetAllPermissions()
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(permissions)
}

func getPermissionHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	permissionID, err := strconv.Atoi(vars["id"])
	if err != nil {
		http.Error(w, "Invalid permission ID", http.StatusBadRequest)
		return
	}

	permission, err := security.GetPermission(permissionID)
	if err != nil {
		http.Error(w, err.Error(), http.StatusNotFound)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(permission)
}