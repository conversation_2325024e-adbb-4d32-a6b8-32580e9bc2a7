package auth

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os"
	"strings"
	"time"

	"web-dashboard/backend/database"
	"web-dashboard/backend/debug"
)

type RecaptchaResponse struct {
	Success     bool     `json:"success"`
	Score       float64  `json:"score"`
	Action      string   `json:"action"`
	ChallengeTS string   `json:"challenge_ts"`
	Hostname    string   `json:"hostname"`
	ErrorCodes  []string `json:"error-codes"`
}

// EnterpriseRecaptchaResponse represents the response from Google Enterprise reCAPTCHA
type EnterpriseRecaptchaResponse struct {
	TokenProperties struct {
		Valid         bool   `json:"valid"`
		InvalidReason string `json:"invalidReason,omitempty"`
		Hostname      string `json:"hostname"`
		Action        string `json:"action"`
		CreateTime    string `json:"createTime"`
	} `json:"tokenProperties"`
	RiskAnalysis struct {
		Score   float64  `json:"score"`
		Reasons []string `json:"reasons"`
	} `json:"riskAnalysis"`
}

// GetRecaptchaSiteKey returns the reCAPTCHA site key from environment variables
func GetRecaptchaSiteKey() string {
	return os.Getenv("RECAPTCHA_SITE_KEY")
}

// GetRecaptchaSiteKeyHandler returns the reCAPTCHA site key if enabled in system settings
func GetRecaptchaSiteKeyHandler(w http.ResponseWriter, r *http.Request) {
	// Check if reCAPTCHA is enabled
	if !getRecaptchaEnabledFromSettings() {
		http.Error(w, "reCAPTCHA is disabled", http.StatusServiceUnavailable)
		return
	}

	siteKey := GetRecaptchaSiteKey()
	if siteKey == "" {
		http.Error(w, "reCAPTCHA site key not configured", http.StatusInternalServerError)
		return
	}

	response := map[string]string{
		"siteKey": siteKey,
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

func verifyRecaptchaWithAction(token, expectedAction string) (bool, error) {
	// First check if reCAPTCHA is enabled in system settings
	recaptchaEnabled := getRecaptchaEnabledFromSettings()
	if !recaptchaEnabled {
		debug.Info("reCAPTCHA verification skipped - disabled in system settings")
		return true, nil // Skip verification if disabled
	}

	secretKey := strings.TrimSpace(os.Getenv("RECAPTCHA_SECRET_KEY"))
	projectID := strings.TrimSpace(os.Getenv("RECAPTCHA_PROJECT_ID"))
	siteKey := strings.TrimSpace(os.Getenv("RECAPTCHA_SITE_KEY"))
	apiKey := strings.TrimSpace(os.Getenv("RECAPTCHA_API_KEY")) // New: Google Cloud API key for Enterprise

	debug.Info("reCAPTCHA verification debug - SecretKey set: %t, ProjectID set: %t, SiteKey set: %t, APIKey set: %t, ExpectedAction: %s",
		secretKey != "", projectID != "", siteKey != "", apiKey != "", expectedAction)

	if token == "" || len(token) < 10 {
		debug.Info("reCAPTCHA verification failed: Invalid or empty token")
		return false, fmt.Errorf("invalid reCAPTCHA token")
	}

	// Check if this is Enterprise reCAPTCHA (requires project ID and API key)
	isEnterprise := projectID != "" && apiKey != ""

	if isEnterprise {
		debug.Info("Verifying Enterprise reCAPTCHA v3 token with Google...")
		return verifyEnterpriseRecaptchaWithAction(token, apiKey, projectID, expectedAction)
	} else {
		debug.Info("Verifying standard reCAPTCHA v3 token with Google...")
		if secretKey == "" {
			debug.Info("reCAPTCHA verification failed: RECAPTCHA_SECRET_KEY not configured")
			return false, fmt.Errorf("RECAPTCHA_SECRET_KEY not configured")
		}
		return verifyStandardRecaptchaWithAction(token, secretKey, expectedAction)
	}
}

func verifyEnterpriseRecaptchaWithAction(token, apiKey, projectID, expectedAction string) (bool, error) {
	siteKey := os.Getenv("RECAPTCHA_SITE_KEY")
	if siteKey == "" {
		debug.Info("Enterprise reCAPTCHA verification failed: RECAPTCHA_SITE_KEY not configured")
		return false, fmt.Errorf("RECAPTCHA_SITE_KEY not configured")
	}

	// Check if we have service account credentials instead of API key
	serviceAccountPath := strings.TrimSpace(os.Getenv("GOOGLE_APPLICATION_CREDENTIALS"))

	if serviceAccountPath != "" {
		// Use service account authentication (recommended)
		return verifyEnterpriseRecaptchaWithServiceAccount(token, projectID, siteKey, expectedAction)
	} else {
		// Fallback to API key method
		return verifyEnterpriseRecaptchaWithAPIKey(token, apiKey, projectID, siteKey, expectedAction)
	}
}

func verifyEnterpriseRecaptchaWithServiceAccount(token, projectID, siteKey, expectedAction string) (bool, error) {
	// This would require importing Google Cloud libraries
	// For now, fall back to API key method with proper authentication headers
	debug.Info("Service account method not implemented, falling back to API key")
	apiKey := strings.TrimSpace(os.Getenv("RECAPTCHA_API_KEY"))
	return verifyEnterpriseRecaptchaWithAPIKey(token, apiKey, projectID, siteKey, expectedAction)
}

func verifyEnterpriseRecaptchaWithAPIKey(token, apiKey, projectID, siteKey, expectedAction string) (bool, error) {
	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	// Create the assessment request body according to Google's Enterprise reCAPTCHA API specification
	requestBody := map[string]interface{}{
		"event": map[string]interface{}{
			"token":          token,
			"siteKey":        siteKey,
			"expectedAction": expectedAction,
		},
	}

	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		debug.Info("Enterprise reCAPTCHA request marshal failed: %v", err)
		return false, err
	}

	debug.Info("Enterprise reCAPTCHA request body: %s", string(jsonData))

	// Create request with proper headers
	url := fmt.Sprintf("https://recaptchaenterprise.googleapis.com/v1/projects/%s/assessments?key=%s", projectID, apiKey)
	debug.Info("Enterprise reCAPTCHA request URL: %s", url)

	req, err := http.NewRequest("POST", url, strings.NewReader(string(jsonData)))
	if err != nil {
		debug.Info("Enterprise reCAPTCHA request creation failed: %v", err)
		return false, err
	}

	// Set proper headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", "Go-http-client/1.1")

	resp, err := client.Do(req)
	if err != nil {
		debug.Info("Enterprise reCAPTCHA HTTP request failed: %v", err)
		return false, err
	}
	defer resp.Body.Close()

	// Read the response body for debugging
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		debug.Info("Enterprise reCAPTCHA response body read failed: %v", err)
		return false, err
	}

	debug.Info("Enterprise reCAPTCHA response status: %d", resp.StatusCode)
	debug.Info("Enterprise reCAPTCHA response body: %s", string(bodyBytes))

	// Check for HTTP errors first
	if resp.StatusCode != http.StatusOK {
		debug.Info("Enterprise reCAPTCHA API error: HTTP %d - %s", resp.StatusCode, string(bodyBytes))

		// Check if it's a 403 Forbidden error and suggest alternatives
		if resp.StatusCode == 403 {
			debug.Info("403 Forbidden: This might be due to:")
			debug.Info("1. reCAPTCHA Enterprise API not enabled in Google Cloud Console")
			debug.Info("2. API key doesn't have proper permissions")
			debug.Info("3. Need to use service account authentication instead")
			debug.Info("Falling back to standard reCAPTCHA verification...")

			// Fall back to standard reCAPTCHA
			secretKey := strings.TrimSpace(os.Getenv("RECAPTCHA_SECRET_KEY"))
			if secretKey != "" {
				return verifyStandardRecaptchaWithAction(token, secretKey, expectedAction)
			}
		}

		return false, fmt.Errorf("reCAPTCHA API returned status %d: %s", resp.StatusCode, string(bodyBytes))
	}

	// Parse the response from the body bytes
	var enterpriseResp EnterpriseRecaptchaResponse
	if err := json.Unmarshal(bodyBytes, &enterpriseResp); err != nil {
		debug.Info("Enterprise reCAPTCHA response decode failed: %v", err)
		return false, err
	}

	debug.Info("Enterprise reCAPTCHA v3 response: Valid=%t, Score=%f, Action=%s, Expected=%s, Reasons=%v",
		enterpriseResp.TokenProperties.Valid, enterpriseResp.RiskAnalysis.Score,
		enterpriseResp.TokenProperties.Action, expectedAction, enterpriseResp.RiskAnalysis.Reasons)

	// Check if token is valid
	if !enterpriseResp.TokenProperties.Valid {
		debug.Info("Enterprise reCAPTCHA verification failed: Token invalid, Reason=%s, Reasons=%v",
			enterpriseResp.TokenProperties.InvalidReason, enterpriseResp.RiskAnalysis.Reasons)
		return false, nil
	}

	// Verify the action matches what we expect
	if enterpriseResp.TokenProperties.Action != expectedAction {
		debug.Info("Enterprise reCAPTCHA action mismatch: Expected=%s, Received=%s", expectedAction, enterpriseResp.TokenProperties.Action)
		return false, nil
	}

	// Check score for Enterprise v3 (score between 0.0 and 1.0, higher is better)
	if enterpriseResp.RiskAnalysis.Score < 0.3 {
		debug.Info("Enterprise reCAPTCHA score too low: %f (minimum required: 0.3)", enterpriseResp.RiskAnalysis.Score)
		return false, nil
	}

	debug.Info("Enterprise reCAPTCHA v3 verification successful: Score=%f, Action=%s", enterpriseResp.RiskAnalysis.Score, expectedAction)
	return true, nil
}

func verifyStandardRecaptchaWithAction(token, secretKey, expectedAction string) (bool, error) {
	// Standard reCAPTCHA verification (fallback)
	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	resp, err := client.PostForm("https://www.google.com/recaptcha/api/siteverify", url.Values{
		"secret":   {secretKey},
		"response": {token},
	})
	if err != nil {
		debug.Info("Standard reCAPTCHA HTTP request failed: %v", err)
		return false, err
	}
	defer resp.Body.Close()

	var recaptchaResp RecaptchaResponse
	if err := json.NewDecoder(resp.Body).Decode(&recaptchaResp); err != nil {
		debug.Info("Standard reCAPTCHA response decode failed: %v", err)
		return false, err
	}

	debug.Info("Standard reCAPTCHA v3 response: Success=%t, Score=%f, Action=%s, Expected=%s, ErrorCodes=%v",
		recaptchaResp.Success, recaptchaResp.Score, recaptchaResp.Action, expectedAction, recaptchaResp.ErrorCodes)

	// For standard v3, check both success and score
	if !recaptchaResp.Success {
		debug.Info("Standard reCAPTCHA verification failed: Success=false, ErrorCodes=%v", recaptchaResp.ErrorCodes)
		return false, nil
	}

	// Verify the action matches what we expect (for v3)
	if recaptchaResp.Action != expectedAction {
		debug.Info("Standard reCAPTCHA action mismatch: Expected=%s, Received=%s", expectedAction, recaptchaResp.Action)
		return false, nil
	}

	// Check score for v3 (v3 always returns a score between 0.0 and 1.0)
	if recaptchaResp.Score < 0.3 {
		debug.Info("Standard reCAPTCHA score too low: %f (minimum required: 0.3)", recaptchaResp.Score)
		return false, nil
	}

	debug.Info("Standard reCAPTCHA v3 verification successful: Score=%f, Action=%s", recaptchaResp.Score, expectedAction)
	return true, nil
}

// getRecaptchaEnabledFromSettings checks if reCAPTCHA is enabled in system settings
func getRecaptchaEnabledFromSettings() bool {
	setting, err := database.GetSystemSetting("recaptcha_enabled")
	if err != nil {
		debug.Info("Failed to get reCAPTCHA enabled setting: %v, defaulting to enabled", err)
		return true // Default to enabled if setting not found
	}

	if setting.DataType == "boolean" {
		if setting.Value == "true" {
			return true
		}
		return false
	}

	// Default to enabled if data type is unexpected
	debug.Info("Unexpected data type for recaptcha_enabled setting: %s, defaulting to enabled", setting.DataType)
	return true
}