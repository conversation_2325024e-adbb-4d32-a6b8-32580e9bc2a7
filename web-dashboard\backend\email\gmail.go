package email

import (
	"context"
	"crypto/rsa"
	"crypto/x509"
	"encoding/base64"
	"encoding/pem"
	"fmt"
	"os"
	"strings"

	"web-dashboard/backend/debug"

	"golang.org/x/oauth2/jwt"
	"google.golang.org/api/gmail/v1"
	"google.golang.org/api/option"
)

type GmailService struct {
	config    *jwt.Config
	fromEmail string
	service   *gmail.Service
}

type EmailMessage struct {
	To      []string
	Subject string
	Body    string
	IsHTML  bool
}

// NewGmailService creates a new Gmail service using service account credentials from environment variables
func NewGmailService() (*GmailService, error) {
	serviceAccountEmail := os.Getenv("GMAIL_SERVICE_ACCOUNT_EMAIL")
	privateKeyPEM := os.Getenv("GMAIL_PRIVATE_KEY")
	clientEmail := os.Getenv("GMAIL_CLIENT_EMAIL")
	tokenURI := os.Getenv("GMAIL_TOKEN_URI")

	if serviceAccountEmail == "" || privateKeyPEM == "" || clientEmail == "" {
		return nil, fmt.Errorf("missing required Gmail service account environment variables")
	}

	if tokenURI == "" {
		tokenURI = "https://oauth2.googleapis.com/token"
	}

	// Parse the private key
	privateKey, err := parsePrivateKey(privateKeyPEM)
	if err != nil {
		return nil, fmt.Errorf("failed to parse private key: %v", err)
	}

	// Convert private key to bytes
	privateKeyBytes := x509.MarshalPKCS1PrivateKey(privateKey)

	// Create JWT config for service account
	config := &jwt.Config{
		Email:        clientEmail,
		PrivateKey:   privateKeyBytes,
		PrivateKeyID: "",
		TokenURL:     tokenURI,
		Scopes:       []string{"https://www.googleapis.com/auth/gmail.send"},
		Subject:      serviceAccountEmail, // Important: impersonate the service account email
	}

	// Create Gmail service
	ctx := context.Background()
	tokenSource := config.TokenSource(ctx)

	gmailService, err := gmail.NewService(ctx, option.WithTokenSource(tokenSource))
	if err != nil {
		return nil, fmt.Errorf("failed to create Gmail service: %v", err)
	}

	return &GmailService{
		config:    config,
		fromEmail: serviceAccountEmail,
		service:   gmailService,
	}, nil
}

// parsePrivateKey parses a PEM-encoded private key
func parsePrivateKey(keyPEM string) (*rsa.PrivateKey, error) {
	// Clean up the key string
	keyPEM = strings.ReplaceAll(keyPEM, "\\n", "\n")

	block, _ := pem.Decode([]byte(keyPEM))
	if block == nil {
		return nil, fmt.Errorf("failed to parse PEM block containing the private key")
	}

	key, err := x509.ParsePKCS8PrivateKey(block.Bytes)
	if err != nil {
		// Try PKCS1 format as fallback
		key, err = x509.ParsePKCS1PrivateKey(block.Bytes)
		if err != nil {
			return nil, fmt.Errorf("failed to parse private key: %v", err)
		}
	}

	rsaKey, ok := key.(*rsa.PrivateKey)
	if !ok {
		return nil, fmt.Errorf("private key is not an RSA key")
	}

	return rsaKey, nil
}

// SendEmail sends an email using the Gmail API
func (gs *GmailService) SendEmail(msg EmailMessage) error {
	if len(msg.To) == 0 {
		return fmt.Errorf("no recipients specified")
	}

	// Create the email message
	emailMessage := gs.createGmailMessage(msg)

	// Send the email
	_, err := gs.service.Users.Messages.Send("me", emailMessage).Do()
	if err != nil {
		return fmt.Errorf("failed to send email: %v", err)
	}

	debug.Info("Email sent successfully to %v", msg.To)
	
	return nil
}

// createGmailMessage creates a Gmail API message from our EmailMessage
func (gs *GmailService) createGmailMessage(msg EmailMessage) *gmail.Message {
	var contentType string
	if msg.IsHTML {
		contentType = "text/html; charset=UTF-8"
	} else {
		contentType = "text/plain; charset=UTF-8"
	}

	// Create the email headers and body
	var messageBuilder strings.Builder

	// Headers
	messageBuilder.WriteString(fmt.Sprintf("From: %s\r\n", gs.fromEmail))
	messageBuilder.WriteString(fmt.Sprintf("To: %s\r\n", strings.Join(msg.To, ", ")))
	messageBuilder.WriteString(fmt.Sprintf("Subject: %s\r\n", msg.Subject))
	messageBuilder.WriteString("MIME-Version: 1.0\r\n")
	messageBuilder.WriteString(fmt.Sprintf("Content-Type: %s\r\n", contentType))
	messageBuilder.WriteString("\r\n")

	// Body
	messageBuilder.WriteString(msg.Body)

	// Encode the message in base64url
	rawMessage := base64.URLEncoding.EncodeToString([]byte(messageBuilder.String()))

	return &gmail.Message{
		Raw: rawMessage,
	}
}

// SendTestEmail sends a test email to verify the configuration
func (gs *GmailService) SendTestEmail(to string) error {
	msg := EmailMessage{
		To:      []string{to},
		Subject: "Test Email from Web Dashboard",
		Body:    "This is a test email to verify that the Gmail integration is working correctly using the Gmail API v1.",
		IsHTML:  false,
	}

	return gs.SendEmail(msg)
}