<script>
  import { Router, Route, navigate } from 'svelte-routing';
  import Home from './routes/Home.svelte';
  import Profile from './routes/Profile.svelte';
  import System from './routes/System.svelte';
  import Auth from './routes/Auth.svelte';
  import Administration from './routes/Administration.svelte';
  import ResetPassword from './routes/ResetPassword.svelte';

  // Clear console logs on startup to see fresh logs
  if (typeof window !== 'undefined') {
    console.clear();
  }

  // Global error handler for unhandled promise rejections
  if (typeof window !== 'undefined') {
    window.addEventListener('unhandledrejection', event => {
      // Filter out Eruda/dev tool errors that we can't control
      const errorString = event.reason?.toString() || '';
      const errorStack = event.reason?.stack || '';
      const errorMessage = event.reason?.message || '';

      // Check for Eruda-related errors in multiple ways
      if (errorString.includes('eruda') || 
          errorString.includes('__replco') ||
          errorStack.includes('eruda') || 
          errorStack.includes('__replco') ||
          errorMessage.includes('Cannot read properties of null') ||
          (event.reason instanceof TypeError && !errorStack.includes('App.svelte'))) {
        console.log('Ignoring Eruda/dev tool error:', event.reason);
        event.preventDefault();
        return;
      }

      console.error('Unhandled promise rejection:', event.reason);
      event.preventDefault(); // Prevent the default browser behavior
    });
  }

  import { onMount } from 'svelte';

  let userInfo = null;
  let isAuthenticated = false;
  let authLoading = true;
  let showDropdown = false;
  let dropdownElement;

  // Use session storage to prevent multiple auth checks across app lifecycle
  const AUTH_CHECK_KEY = 'auth_check_completed';
  const AUTH_DATA_KEY = 'auth_user_data';

  // Check authentication status on app load
  async function checkAuth() {
    console.log('App: Checking authentication...');

    try {
      const response = await fetch('/api/user/info', {
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      });
      console.log('App: Auth response status:', response.status);

      if (response.ok) {
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
          const data = await response.json();
          if (data && data.email && !data.error) {
            console.log('App: User authenticated:', data.email);
            userInfo = data;
            isAuthenticated = true;

            // Store auth data in session storage
            sessionStorage.setItem(AUTH_DATA_KEY, JSON.stringify(data));
            sessionStorage.setItem(AUTH_CHECK_KEY, 'true');
          } else {
            console.log('App: Auth check failed: Invalid user data or API error:', data);
            // Clear any stale session data
            sessionStorage.removeItem(AUTH_DATA_KEY);
            sessionStorage.removeItem(AUTH_CHECK_KEY);
            userInfo = null;
            isAuthenticated = false;
          }
        } else {
          console.log('App: Auth check failed: Non-JSON response, content-type:', contentType);
          // Clear any stale session data
          sessionStorage.removeItem(AUTH_DATA_KEY);
          sessionStorage.removeItem(AUTH_CHECK_KEY);
          userInfo = null;
          isAuthenticated = false;
        }
      } else {
        console.log('App: User not authenticated, status:', response.status);
        // Clear any stale session data
        sessionStorage.removeItem(AUTH_DATA_KEY);
        sessionStorage.removeItem(AUTH_CHECK_KEY);
        userInfo = null;
        isAuthenticated = false;
      }
    } catch (error) {
      console.log('App: Auth check failed:', error);
      // Clear any stale session data
      sessionStorage.removeItem(AUTH_DATA_KEY);
      sessionStorage.removeItem(AUTH_CHECK_KEY);
      userInfo = null;
      isAuthenticated = false;
    } finally {
      authLoading = false;

      // If not authenticated and not on allowed public pages, redirect to /auth
      if (!isAuthenticated) {
        const currentPath = window.location.pathname;
        const allowedPublicPaths = ['/auth', '/reset-password'];
        
        if (!allowedPublicPaths.includes(currentPath)) {
          console.log('App: User not authenticated, redirecting to /auth');
          window.location.href = '/auth';
          return;
        }
        console.log('App: User not authenticated, on allowed public page:', currentPath);
      }
    }
  }

  // Handle clicks outside dropdown
  function handleClickOutside(event) {
    if (showDropdown && dropdownElement && !dropdownElement.contains(event.target)) {
      showDropdown = false;
    }
  }

  // Only run auth check once when component mounts
  onMount(() => {
    checkAuth();

    // Add global click listener
    document.addEventListener('click', handleClickOutside);

    return () => {
      // Cleanup listener
      document.removeEventListener('click', handleClickOutside);
    };
  });

  function navigateToPage(page) {
    showDropdown = false;
    navigate('/' + page);
  }



  function getUserInitial(email) {
    if (!email) return 'U';
    return email.charAt(0).toUpperCase();
  }

  // Clear session storage on logout
  function logout() {
    showDropdown = false;
    sessionStorage.removeItem('auth_check_completed');
    sessionStorage.removeItem('auth_user_data');
    window.location.href = '/logout';
  }

  // Track current path for active nav highlighting
  let currentPath = '/';
</script>

<svelte:head>
  <title>Dashboard</title>
</svelte:head>

<main>
  {#if authLoading}
    <div class="auth-loading">
      <p>Loading...</p>
    </div>
  {:else if isAuthenticated}
    <nav>
      <div class="nav-brand">
        <h2>Dashboard</h2>
      </div>

      <div class="nav-user">
        <div class="user-dropdown" role="button" tabindex="0" on:click={() => showDropdown = !showDropdown} bind:this={dropdownElement}>
          <div class="user-icon">
            <span class="user-initial">{getUserInitial(userInfo?.email)}</span>
          </div>
          {#if showDropdown}
            <div class="dropdown-menu">
              <div class="user-info">
                <div class="user-email">{userInfo?.email || 'User'}</div>
              </div>
              <div class="dropdown-divider"></div>
              <button class="dropdown-item" on:click={() => navigateToPage('profile')}>
                <span class="dropdown-icon">👤</span>
                Show Profile
              </button>
              <button class="dropdown-item" on:click={() => navigateToPage('system')}>
                <span class="dropdown-icon">🖥️</span>
                System
              </button>
              <button class="dropdown-item" on:click={() => navigateToPage('administration')}>
                <span class="dropdown-icon">⚙️</span>
                Administration
              </button>
              <div class="dropdown-divider"></div>
              <button class="dropdown-item logout-item" on:click={logout}>
                <span class="dropdown-icon">🚪</span>
                Logout
              </button>
            </div>
          {/if}
        </div>
      </div>
    </nav>

    <div class="content">
      <Router>
        <Route path="/" component={Home} />
        <Route path="/home" component={Home} />
        <Route path="/profile" component={Profile} userInfo={userInfo} />
        <Route path="/system" component={System} userInfo={userInfo} />
		<Route path="/administration" component={Administration} userInfo={userInfo} />
        <Route path="/reset-password" component={ResetPassword} />
      </Router>
    </div>
  {:else}
    <!-- Handle public routes for unauthenticated users -->
    {#if window.location.pathname === '/reset-password'}
      <ResetPassword />
    {:else}
      <Auth />
    {/if}
  {/if}
</main>

<style>
  main {
    min-height: 100vh;
  }

  nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
  }

  .nav-brand h2 {
    margin: 0;
    color: #333;
  }



  .nav-user {
    display: flex;
    align-items: center;
    position: relative;
  }

  .user-dropdown {
    position: relative;
    cursor: pointer;
  }

  .user-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 16px;
    transition: transform 0.2s ease;
  }

  .user-icon:hover {
    transform: scale(1.05);
  }

  .user-initial {
    user-select: none;
  }

  .dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: 8px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    min-width: 280px;
    z-index: 1000;
    border: 1px solid #e1e5e9;
    overflow: hidden;
    animation: dropdownFadeIn 0.2s ease-out;
  }

  @keyframes dropdownFadeIn {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .user-info {
    padding: 16px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e1e5e9;
  }

  .user-email {
    font-size: 14px;
    color: #495057;
    font-weight: 500;
  }

  .dropdown-item {
    width: 100%;
    padding: 12px 20px;
    border: none;
    background: none;
    text-align: left;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 14px;
    color: #495057;
    transition: background-color 0.2s ease;
  }

  .dropdown-item:hover {
    background-color: #f8f9fa;
  }

  .dropdown-item.logout-item {
    color: #dc3545;
  }

  .dropdown-item.logout-item:hover {
    background-color: #fdf2f2;
  }



  .dropdown-icon {
    font-size: 16px;
    width: 20px;
    text-align: center;
  }

  .dropdown-divider {
    height: 1px;
    background-color: #e1e5e9;
    margin: 8px 0;
  }

  .content {
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto;
  }

  .auth-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    font-size: 1.2rem;
    color: #666;
  }
</style>