=== TEST RESULTS ===
?   	web-dashboard/backend/config	[no test files]
?   	web-dashboard/backend/session	[no test files]
?   	web-dashboard/backend/system_settings	[no test files]
?   	web-dashboard/backend/security	[no test files]
?   	web-dashboard/backend/users	[no test files]
?   	web-dashboard/backend/email	[no test files]
?   	web-dashboard/backend/testutils	[no test files]
?   	web-dashboard/backend/debug	[no test files]
=== RUN   TestBasicFunctionality
=== RUN   TestBasicFunctionality/Environment_Setup
    main_test.go:31: ✅ Test environment properly configured
--- PASS: TestBasicFunctionality (0.00s)
    --- PASS: TestBasicFunctionality/Environment_Setup (0.00s)
PASS
ok  	web-dashboard/backend	1.171s
=== RUN   TestCheckSessionPermission
=== RUN   TestCheckSessionPermission/nil_session_data
=== RUN   TestCheckSessionPermission/nil_permissions
=== RUN   TestCheckSessionPermission/wildcard_permission
=== RUN   TestCheckSessionPermission/specific_permission_match
=== RUN   TestCheckSessionPermission/no_permission_match
--- PASS: TestCheckSessionPermission (0.00s)
    --- PASS: TestCheckSessionPermission/nil_session_data (0.00s)
    --- PASS: TestCheckSessionPermission/nil_permissions (0.00s)
    --- PASS: TestCheckSessionPermission/wildcard_permission (0.00s)
    --- PASS: TestCheckSessionPermission/specific_permission_match (0.00s)
    --- PASS: TestCheckSessionPermission/no_permission_match (0.00s)
=== RUN   TestRequirePermissions
=== RUN   TestRequirePermissions/valid_permissions
=== RUN   TestRequirePermissions/invalid_permissions
--- PASS: TestRequirePermissions (0.00s)
    --- PASS: TestRequirePermissions/valid_permissions (0.00s)
    --- PASS: TestRequirePermissions/invalid_permissions (0.00s)
=== RUN   TestPermissionsRoutes
=== RUN   TestPermissionsRoutes/GET_/permission/permissions_without_auth_returns_401
--- PASS: TestPermissionsRoutes (0.00s)
    --- PASS: TestPermissionsRoutes/GET_/permission/permissions_without_auth_returns_401 (0.00s)
=== RUN   TestRedisRoutes
=== RUN   TestRedisRoutes/GET_/redis/stats_without_auth_returns_401
--- PASS: TestRedisRoutes (0.00s)
    --- PASS: TestRedisRoutes/GET_/redis/stats_without_auth_returns_401 (0.00s)
=== RUN   TestSecurityGroupsRoutes
=== RUN   TestSecurityGroupsRoutes/GET_/security-groups_without_auth_returns_401
--- PASS: TestSecurityGroupsRoutes (0.00s)
    --- PASS: TestSecurityGroupsRoutes/GET_/security-groups_without_auth_returns_401 (0.00s)
=== RUN   TestSystemSettingsRoutes
=== RUN   TestSystemSettingsRoutes/GET_/system-settings/users_without_auth_returns_401
--- PASS: TestSystemSettingsRoutes (0.00s)
    --- PASS: TestSystemSettingsRoutes/GET_/system-settings/users_without_auth_returns_401 (0.00s)
=== RUN   TestUsersRoutes
=== RUN   TestUsersRoutes/GET_/user/users_without_auth_returns_401
--- PASS: TestUsersRoutes (0.02s)
    --- PASS: TestUsersRoutes/GET_/user/users_without_auth_returns_401 (0.00s)
PASS
ok  	web-dashboard/backend/api	1.185s
Failed to initialize database for auth tests: failed to ping database: failed to connect to `user= database=`:
	[::1]:5432 (localhost): tls error: server refused TLS connection
	127.0.0.1:5432 (localhost): tls error: server refused TLS connection
FAIL	web-dashboard/backend/auth	0.302s
=== RUN   TestInitDB
    database_test.go:30: 
        	Error Trace:	C:/Go Projects/web-dashboard/web-dashboard/backend/database/database_test.go:30
        	Error:      	Received unexpected error:
        	            	failed to ping database: failed to connect to `user= database=`:
        	            		[::1]:5432 (localhost): tls error: server refused TLS connection
        	            		127.0.0.1:5432 (localhost): tls error: server refused TLS connection
        	Test:       	TestInitDB
        	Messages:   	Database initialization must succeed with test secrets
--- FAIL: TestInitDB (0.07s)
=== RUN   TestGetRecords
    database_test.go:47: 
        	Error Trace:	C:/Go Projects/web-dashboard/web-dashboard/backend/database/database_test.go:47
        	Error:      	Received unexpected error:
        	            	failed to query records: failed to connect to `user= database=`:
        	            		[::1]:5432 (localhost): tls error: server refused TLS connection
        	            		127.0.0.1:5432 (localhost): tls error: server refused TLS connection
        	Test:       	TestGetRecords
--- FAIL: TestGetRecords (0.16s)
=== RUN   TestSystemSettings
=== RUN   TestSystemSettings/SetAndGetSystemSetting
2025/07/25 15:48:07 [ERROR] failed to set setting: failed to connect to `user= database=`:
	[::1]:5432 (localhost): tls error: server refused TLS connection
	127.0.0.1:5432 (localhost): tls error: server refused TLS connection
    database_test.go:59: 
        	Error Trace:	C:/Go Projects/web-dashboard/web-dashboard/backend/database/database_test.go:59
        	Error:      	Received unexpected error:
        	            	failed to set setting: failed to connect to `user= database=`:
        	            		[::1]:5432 (localhost): tls error: server refused TLS connection
        	            		127.0.0.1:5432 (localhost): tls error: server refused TLS connection
        	Test:       	TestSystemSettings/SetAndGetSystemSetting
=== RUN   TestSystemSettings/GetNonExistentSetting
2025/07/25 15:48:07 [ERROR] failed to get setting: failed to connect to `user= database=`:
	[::1]:5432 (localhost): tls error: server refused TLS connection
	127.0.0.1:5432 (localhost): tls error: server refused TLS connection
2025/07/25 15:48:07 [ERROR] failed to get setting: failed to connect to `user= database=`:
	[::1]:5432 (localhost): tls error: server refused TLS connection
	127.0.0.1:5432 (localhost): tls error: server refused TLS connection
=== RUN   TestSystemSettings/GetAllSystemSettings
2025/07/25 15:48:07 [ERROR] failed to query settings: failed to connect to `user= database=`:
	[::1]:5432 (localhost): tls error: server refused TLS connection
	127.0.0.1:5432 (localhost): tls error: server refused TLS connection
    database_test.go:89: 
        	Error Trace:	C:/Go Projects/web-dashboard/web-dashboard/backend/database/database_test.go:89
        	Error:      	Received unexpected error:
        	            	failed to query settings: failed to connect to `user= database=`:
        	            		[::1]:5432 (localhost): tls error: server refused TLS connection
        	            		127.0.0.1:5432 (localhost): tls error: server refused TLS connection
        	Test:       	TestSystemSettings/GetAllSystemSettings
    database_test.go:90: 
        	Error Trace:	C:/Go Projects/web-dashboard/web-dashboard/backend/database/database_test.go:90
        	Error:      	Expected value not to be nil.
        	Test:       	TestSystemSettings/GetAllSystemSettings
--- FAIL: TestSystemSettings (0.33s)
    --- FAIL: TestSystemSettings/SetAndGetSystemSetting (0.05s)
    --- PASS: TestSystemSettings/GetNonExistentSetting (0.23s)
    --- FAIL: TestSystemSettings/GetAllSystemSettings (0.06s)
=== RUN   TestRebuildSchema
    database_test.go:101: 
        	Error Trace:	C:/Go Projects/web-dashboard/web-dashboard/backend/database/database_test.go:101
        	Error:      	Received unexpected error:
        	            	failed to rebuild schema: failed to connect to `user= database=`:
        	            		[::1]:5432 (localhost): tls error: server refused TLS connection
        	            		127.0.0.1:5432 (localhost): tls error: server refused TLS connection
        	Test:       	TestRebuildSchema
--- FAIL: TestRebuildSchema (0.04s)
FAIL
FAIL	web-dashboard/backend/database	1.410s
FAIL
