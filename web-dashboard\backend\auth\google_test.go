
package auth

import (
	"net/http"
	"net/http/httptest"
	"net/url"
	"os"
	"testing"

	"web-dashboard/backend/database"
	"web-dashboard/backend/users"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestInitGoogleOAuth(t *testing.T) {
	// Set test environment variables
	oldClientID := os.Getenv("GOOGLE_CLIENT_ID")
	oldClientSecret := os.Getenv("GOOGLE_CLIENT_SECRET")
	defer func() {
		os.Setenv("GOOGLE_CLIENT_ID", oldClientID)
		os.Setenv("GOOGLE_CLIENT_SECRET", oldClientSecret)
	}()

	os.Setenv("GOOGLE_CLIENT_ID", "test-client-id")
	os.Setenv("GOOGLE_CLIENT_SECRET", "test-client-secret")

	// Initialize OAuth
	InitGoogleOAuth()

	// Verify config is set
	assert.NotNil(t, config)
	assert.Equal(t, "test-client-id", config.ClientID)
	assert.Equal(t, "test-client-secret", config.ClientSecret)
	assert.Contains(t, config.RedirectURL, "/callback")
	assert.Contains(t, config.Scopes, "email")
	assert.Contains(t, config.Scopes, "profile")
}

func TestHandleGoogleOAuth(t *testing.T) {
	// Set test environment variables
	oldClientID := os.Getenv("GOOGLE_CLIENT_ID")
	oldClientSecret := os.Getenv("GOOGLE_CLIENT_SECRET")
	defer func() {
		os.Setenv("GOOGLE_CLIENT_ID", oldClientID)
		os.Setenv("GOOGLE_CLIENT_SECRET", oldClientSecret)
	}()

	tests := []struct {
		name           string
		clientID       string
		clientSecret   string
		expectedStatus int
		expectRedirect bool
	}{
		{
			name:           "valid OAuth config redirects to Google",
			clientID:       "test-client-id",
			clientSecret:   "test-client-secret",
			expectedStatus: http.StatusFound,
			expectRedirect: true,
		},
		{
			name:           "missing client ID returns error",
			clientID:       "",
			clientSecret:   "test-client-secret",
			expectedStatus: http.StatusInternalServerError,
			expectRedirect: false,
		},
		{
			name:           "missing client secret returns error",
			clientID:       "test-client-id",
			clientSecret:   "",
			expectedStatus: http.StatusInternalServerError,
			expectRedirect: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			os.Setenv("GOOGLE_CLIENT_ID", tt.clientID)
			os.Setenv("GOOGLE_CLIENT_SECRET", tt.clientSecret)

			// Reinitialize OAuth config
			InitGoogleOAuth()

			req := httptest.NewRequest("GET", "/auth?provider=google", nil)
			rr := httptest.NewRecorder()

			HandleGoogleOAuth(rr, req)

			assert.Equal(t, tt.expectedStatus, rr.Code)

			if tt.expectRedirect {
				location := rr.Header().Get("Location")
				assert.Contains(t, location, "accounts.google.com")
				assert.Contains(t, location, "oauth2")
			}
		})
	}
}

func TestGoogleCallbackHandler(t *testing.T) {
	// Set test environment variables
	os.Setenv("GOOGLE_CLIENT_ID", "test-client-id")
	os.Setenv("GOOGLE_CLIENT_SECRET", "test-client-secret")
	InitGoogleOAuth()

	tests := []struct {
		name           string
		queryParams    string
		expectedStatus int
		expectRedirect bool
	}{
		{
			name:           "missing state parameter",
			queryParams:    "code=test-code",
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "invalid state parameter",
			queryParams:    "state=invalid&code=test-code",
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "missing code parameter",
			queryParams:    "state=state",
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "valid state but no actual OAuth flow",
			queryParams:    "state=state&code=test-code",
			expectedStatus: http.StatusInternalServerError, // Will fail at token exchange
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := httptest.NewRequest("GET", "/callback?"+tt.queryParams, nil)
			rr := httptest.NewRecorder()

			GoogleCallbackHandler(rr, req)

			assert.Equal(t, tt.expectedStatus, rr.Code)

			if tt.expectRedirect {
				location := rr.Header().Get("Location")
				assert.NotEmpty(t, location)
			}
		})
	}
}

func TestCreateOrGetGoogleUser(t *testing.T) {
	tests := []struct {
		name        string
		email       string
		expectError bool
	}{
		{
			name:        "create new Google user",
			email:       "<EMAIL>",
			expectError: false,
		},
		{
			name:        "get existing Google user",
			email:       "<EMAIL>",
			expectError: false,
		},
		{
			name:        "invalid email format",
			email:       "invalid-email",
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// For existing user test, create the user first
			if tt.name == "get existing Google user" {
				_, err := users.CreateOrUpdateUser(tt.email, "google")
				require.NoError(t, err)
			}

			user, err := CreateOrGetGoogleUser(tt.email)

			if tt.expectError {
				assert.Error(t, err)
				assert.Nil(t, user)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, user)
				assert.Equal(t, tt.email, user.Email)
				assert.Equal(t, "google", user.LastLoginMethod)
			}

			// Cleanup
			if !tt.expectError {
				database.DB.Exec("DELETE FROM users WHERE email = $1", tt.email)
			}
		})
	}
}

func TestGoogleOAuthWithSystemSettings(t *testing.T) {
	// Test that OAuth respects system settings for site URL
	
	// Set environment variables
	os.Setenv("GOOGLE_CLIENT_ID", "test-client-id")
	os.Setenv("GOOGLE_CLIENT_SECRET", "test-client-secret")
	
	// This test would require mocking database.GetSettingString
	// For now, we'll test the initialization logic
	InitGoogleOAuth()
	
	assert.NotNil(t, config)
	assert.Equal(t, "test-client-id", config.ClientID)
	assert.Equal(t, "test-client-secret", config.ClientSecret)
	
	// The redirect URL should be constructed properly
	// In production, this would use the system setting
	assert.Contains(t, config.RedirectURL, "/callback")
}

func TestGoogleOAuthErrorHandling(t *testing.T) {
	// Test various error scenarios
	
	tests := []struct {
		name         string
		setupFunc    func()
		expectedCode int
	}{
		{
			name: "OAuth not configured",
			setupFunc: func() {
				os.Setenv("GOOGLE_CLIENT_ID", "")
				os.Setenv("GOOGLE_CLIENT_SECRET", "")
				InitGoogleOAuth()
			},
			expectedCode: http.StatusInternalServerError,
		},
		{
			name: "Partial OAuth configuration",
			setupFunc: func() {
				os.Setenv("GOOGLE_CLIENT_ID", "test-id")
				os.Setenv("GOOGLE_CLIENT_SECRET", "")
				InitGoogleOAuth()
			},
			expectedCode: http.StatusInternalServerError,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupFunc()

			req := httptest.NewRequest("GET", "/auth?provider=google", nil)
			rr := httptest.NewRecorder()

			HandleGoogleOAuth(rr, req)

			assert.Equal(t, tt.expectedCode, rr.Code)
		})
	}
}

func TestGoogleCallbackStateValidation(t *testing.T) {
	// Initialize OAuth
	os.Setenv("GOOGLE_CLIENT_ID", "test-client-id")
	os.Setenv("GOOGLE_CLIENT_SECRET", "test-client-secret")
	InitGoogleOAuth()

	tests := []struct {
		name     string
		state    string
		expected int
	}{
		{
			name:     "valid state",
			state:    "state",
			expected: http.StatusInternalServerError, // Will fail at token exchange but state is valid
		},
		{
			name:     "invalid state",
			state:    "invalid",
			expected: http.StatusBadRequest,
		},
		{
			name:     "empty state",
			state:    "",
			expected: http.StatusBadRequest,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			u, _ := url.Parse("/callback")
			q := u.Query()
			q.Set("code", "test-code")
			if tt.state != "" {
				q.Set("state", tt.state)
			}
			u.RawQuery = q.Encode()

			req := httptest.NewRequest("GET", u.String(), nil)
			rr := httptest.NewRecorder()

			GoogleCallbackHandler(rr, req)

			assert.Equal(t, tt.expected, rr.Code)
		})
	}
}
