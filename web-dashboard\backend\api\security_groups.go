package api

import (
	"encoding/json"
	"net/http"
	"strconv"
	"web-dashboard/backend/security"

	"github.com/gorilla/mux"
)

type CreateSecurityGroupRequest struct {
	Name        string `json:"name"`
	Description string `json:"description"`
}

type UpdateSecurityGroupRequest struct {
	Name        string `json:"name"`
	Description string `json:"description"`
}

type AddUserToSecurityGroupRequest struct {
	UserID int `json:"user_id"`
}

type AddPermissionToSecurityGroupRequest struct {
	PermissionID int `json:"permission_id"`
}

// AttachSecurityGroupRoutes attaches all security group-related routes to the router
func AttachSecurityGroupRoutes(router *mux.Router) {
	router.HandleFunc("/security-groups", RequireSecurityGroupManagement(getSecurityGroupsHandler)).Methods("GET")
	router.HandleFunc("/security-groups", RequireSecurityGroupManagement(createSecurityGroupHandler)).Methods("POST")
	router.HandleFunc("/security-groups/{id:[0-9]+}", RequireSecurityGroupManagement(getSecurityGroupHandler)).Methods("GET")
	router.HandleFunc("/security-groups/{id:[0-9]+}", RequireSecurityGroupManagement(updateSecurityGroupHandler)).Methods("PUT")
	router.HandleFunc("/security-groups/{id:[0-9]+}", RequireSecurityGroupManagement(deleteSecurityGroupHandler)).Methods("DELETE")
	router.HandleFunc("/security-groups/{id:[0-9]+}/members", RequireSecurityGroupManagement(getSecurityGroupMembersHandler)).Methods("GET")
	router.HandleFunc("/security-groups/{id:[0-9]+}/members", RequireSecurityGroupManagement(addUserToSecurityGroupHandler)).Methods("POST")
	router.HandleFunc("/security-groups/{id:[0-9]+}/members/{userID:[0-9]+}", RequireSecurityGroupManagement(removeUserFromSecurityGroupHandler)).Methods("DELETE")
	router.HandleFunc("/security-groups/{id:[0-9]+}/permissions", RequireSecurityGroupManagement(getSecurityGroupPermissionsHandler)).Methods("GET")
	router.HandleFunc("/security-groups/{id:[0-9]+}/permissions", RequireSecurityGroupManagement(addPermissionToSecurityGroupHandler)).Methods("POST")
	router.HandleFunc("/security-groups/{id:[0-9]+}/permissions/{permissionID:[0-9]+}", RequireSecurityGroupManagement(removePermissionFromSecurityGroupHandler)).Methods("DELETE")
}

func getSecurityGroupsHandler(w http.ResponseWriter, r *http.Request) {
	groups, err := security.GetAllSecurityGroups()
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(groups)
}

func createSecurityGroupHandler(w http.ResponseWriter, r *http.Request) {
	var req CreateSecurityGroupRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	if req.Name == "" {
		http.Error(w, "Security group name is required", http.StatusBadRequest)
		return
	}

	group, err := security.CreateSecurityGroup(req.Name, req.Description)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusCreated)
	json.NewEncoder(w).Encode(group)
}

func getSecurityGroupHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	securityGroupID, err := strconv.Atoi(vars["id"])
	if err != nil {
		http.Error(w, "Invalid security group ID", http.StatusBadRequest)
		return
	}

	group, err := security.GetSecurityGroup(securityGroupID)
	if err != nil {
		http.Error(w, err.Error(), http.StatusNotFound)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(group)
}

func updateSecurityGroupHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	securityGroupID, err := strconv.Atoi(vars["id"])
	if err != nil {
		http.Error(w, "Invalid security group ID", http.StatusBadRequest)
		return
	}

	var req UpdateSecurityGroupRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	if req.Name == "" {
		http.Error(w, "Security group name is required", http.StatusBadRequest)
		return
	}

	err = security.UpdateSecurityGroup(securityGroupID, req.Name, req.Description)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusOK)
}

func deleteSecurityGroupHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	securityGroupID, err := strconv.Atoi(vars["id"])
	if err != nil {
		http.Error(w, "Invalid security group ID", http.StatusBadRequest)
		return
	}

	err = security.DeleteSecurityGroup(securityGroupID)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusNoContent)
}

func getSecurityGroupMembersHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	groupID, err := strconv.Atoi(vars["id"])
	if err != nil {
		http.Error(w, "Invalid group ID", http.StatusBadRequest)
		return
	}

	members, err := security.GetSecurityGroupMembers(groupID)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(members)
}

func addUserToSecurityGroupHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	securityGroupID, err := strconv.Atoi(vars["id"])
	if err != nil {
		http.Error(w, "Invalid security group ID", http.StatusBadRequest)
		return
	}

	var req AddUserToSecurityGroupRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	err = security.AddUserToSecurityGroup(req.UserID, securityGroupID)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusCreated)
}

func removeUserFromSecurityGroupHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	groupID, err := strconv.Atoi(vars["id"])
	if err != nil {
		http.Error(w, "Invalid group ID", http.StatusBadRequest)
		return
	}

	userID, err := strconv.Atoi(vars["userID"])
	if err != nil {
		http.Error(w, "Invalid user ID", http.StatusBadRequest)
		return
	}

	err = security.RemoveUserFromSecurityGroup(userID, groupID)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusNoContent)
}

func getSecurityGroupPermissionsHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	groupID, err := strconv.Atoi(vars["id"])
	if err != nil {
		http.Error(w, "Invalid group ID", http.StatusBadRequest)
		return
	}

	permissions, err := security.GetSecurityGroupPermissions(groupID)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(permissions)
}

func addPermissionToSecurityGroupHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	securityGroupID, err := strconv.Atoi(vars["id"])
	if err != nil {
		http.Error(w, "Invalid security group ID", http.StatusBadRequest)
		return
	}

	var req AddPermissionToSecurityGroupRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	err = security.AddPermissionToSecurityGroup(securityGroupID, req.PermissionID)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusCreated)
}

func removePermissionFromSecurityGroupHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	groupID, err := strconv.Atoi(vars["id"])
	if err != nil {
		http.Error(w, "Invalid group ID", http.StatusBadRequest)
		return
	}

	permissionID, err := strconv.Atoi(vars["permissionID"])
	if err != nil {
		http.Error(w, "Invalid permission ID", http.StatusBadRequest)
		return
	}

	err = security.RemovePermissionFromSecurityGroup(permissionID, groupID)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusNoContent)
}