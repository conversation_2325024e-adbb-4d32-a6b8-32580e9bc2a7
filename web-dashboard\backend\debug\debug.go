package debug

import (
	"fmt"
	"log"
	"web-dashboard/backend/config"
)

// logMessage is the internal logging function that formats and outputs log messages
// based on the configured log level.
//
// Parameters:
//   - level: The LogLevel indicating message severity (TRACE, DEBUG, INFO, WARN, ERROR)
//   - format: A format string for the message (printf style)
//   - args: Variable arguments to be formatted into the message
//
// The function first checks if the message should be logged based on the current
// log level configuration. If the message level is below the configured level,
// the message is skipped. Otherwise, it formats the message with a level prefix
// and outputs it using the standard log package.
func logMessage(level config.LogLevel, format string, args ...interface{}) {
	if !config.ShouldLog(level) {
		return
	}

	prefix := fmt.Sprintf("[%s]", level.String())
	message := fmt.Sprintf(format, args...)

	// Use log.Print instead of log.Printf to avoid duplicate formatting
	// and remove file information since all logs come through debug.go
	log.Print(prefix + " " + message)
}

// Trace logs a trace-level message (most verbose)
func Trace(format string, args ...interface{}) {
	logMessage(config.TRACE, format, args...)
}

// Debug logs a debug-level message
func Debug(format string, args ...interface{}) {
	logMessage(config.DEBUG, format, args...)
}

// Info logs an info-level message
func Info(format string, args ...interface{}) {
	logMessage(config.INFO, format, args...)
}

// Warn logs a warning-level message
func Warn(format string, args ...interface{}) {
	logMessage(config.WARN, format, args...)
}

// Error logs an error-level message
func Error(format string, args ...interface{}) {
	logMessage(config.ERROR, format, args...)
}

// GetCurrentLogLevel returns the current effective log level
func GetCurrentLogLevel() string {
	return config.GetCurrentLogLevelString()
}
