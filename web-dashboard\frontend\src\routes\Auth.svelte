<script>
  import { push } from 'svelte-spa-router';
  import { onMount, onDestroy } from 'svelte';

  let email = '';
  let password = '';
  let error = '';
  let loading = false;
  let recaptchaLoaded = false;
  let recaptchaSiteKey = null;
  let recaptchaEnabled = true;

  onMount(async () => {
    // First check if reCAPTCHA is enabled
    try {
      const enabledResponse = await fetch('/api/system-settings/settings/recaptcha_enabled');
      if (enabledResponse.ok) {
        const enabledData = await enabledResponse.json();
        recaptchaEnabled = enabledData.value === true || enabledData.value === 'true';
        console.log('reCAPTCHA enabled setting:', recaptchaEnabled);

        if (!recaptchaEnabled) {
          console.log('reCAPTCHA is disabled, skipping initialization');
          recaptchaLoaded = false; // Mark as not loaded since it's disabled
          return;
        }
      } else {
        console.log('Could not fetch reCAPTCHA enabled setting, assuming enabled');
      }
    } catch (err) {
      console.log('Error checking reCAPTCHA enabled setting, assuming enabled:', err);
    }

    // Use site key from window if already loaded, otherwise fetch it
    if (window.recaptchaSiteKey) {
      recaptchaSiteKey = window.recaptchaSiteKey;
      console.log('Using cached reCAPTCHA site key:', recaptchaSiteKey);
    } else {
      try {
        const response = await fetch('/api/recaptcha/sitekey');
        if (response.ok) {
          const data = await response.json();
          recaptchaSiteKey = data.siteKey;
          window.recaptchaSiteKey = data.siteKey;
          console.log('reCAPTCHA v3 site key loaded:', recaptchaSiteKey);
        } else {
          console.error('Failed to fetch reCAPTCHA site key:', response.status);
          if (response.status === 503) {
            // Service unavailable - reCAPTCHA is disabled
            console.log('reCAPTCHA is disabled on server');
            recaptchaEnabled = false;
            return;
          }
          error = 'reCAPTCHA configuration error. Please contact support.';
          return;
        }
      } catch (err) {
        console.error('Error fetching reCAPTCHA site key:', err);
        error = 'Failed to load reCAPTCHA. Please refresh and try again.';
        return;
      }
    }

    // Check if Enterprise reCAPTCHA script is loaded
    let recaptchaCheckAttempts = 0;
    const maxRecaptchaAttempts = 150; // 15 seconds max wait

    const checkRecaptcha = () => {
      recaptchaCheckAttempts++;

      if (window.grecaptcha && window.grecaptcha.enterprise) {
        recaptchaLoaded = true;
        console.log('Enterprise reCAPTCHA v3 script loaded successfully with site key:', recaptchaSiteKey);
      } else if (window.recaptchaLoaded) {
        // Script loaded but enterprise object not ready yet
        setTimeout(checkRecaptcha, 100);
      } else if (recaptchaCheckAttempts < maxRecaptchaAttempts) {
        console.log('Waiting for Enterprise reCAPTCHA to load... attempt', recaptchaCheckAttempts);
        setTimeout(checkRecaptcha, 100);
      } else {
        console.error('Enterprise reCAPTCHA failed to load after', maxRecaptchaAttempts, 'attempts');
        console.log('Window object state:', {
          grecaptcha: !!window.grecaptcha,
          enterprise: !!(window.grecaptcha && window.grecaptcha.enterprise),
          recaptchaLoaded: window.recaptchaLoaded,
          recaptchaLoading: window.recaptchaLoading
        });
        error = 'reCAPTCHA failed to load. Please refresh the page and try again.';
      }
    };
    checkRecaptcha();
  });

  async function getRecaptchaToken(action) {
    if (!recaptchaLoaded || !window.grecaptcha || !window.grecaptcha.enterprise) {
      console.error('reCAPTCHA error: Enterprise reCAPTCHA not loaded properly');
      throw new Error('Enterprise reCAPTCHA not loaded');
    }

    if (!recaptchaSiteKey) {
      console.error('reCAPTCHA error: Site key not available');
      throw new Error('reCAPTCHA site key not loaded');
    }

    try {
      console.log('Calling Enterprise reCAPTCHA execute for action:', action);
      const token = await window.grecaptcha.enterprise.execute(recaptchaSiteKey, { action: action });
      console.log('Enterprise reCAPTCHA v3 token generated for action:', action);
      return token;
    } catch (error) {
      console.error('reCAPTCHA error:', error);
      throw error;
    }
  }

  let requiresTotp = false;
  let totpCode = '';
  let loginError = '';
  let loginSuccess = '';

  async function handleLocalAuth(event) {
    event.preventDefault();
    loading = true;
    error = '';
    loginError = '';

    const formData = new FormData();
    formData.append('email', email);
    formData.append('password', password);
    
    // Add TOTP code if required and provided
    if (requiresTotp && totpCode) {
      formData.append('totp_code', totpCode);
    }

    // Get reCAPTCHA response for login (only if enabled)
    if (recaptchaEnabled) {
      try {
        const recaptchaToken = await getRecaptchaToken('login');
        formData.append('recaptcha_token', recaptchaToken);
      } catch (err) {
        console.error('reCAPTCHA error:', err);
        error = 'reCAPTCHA not loaded. Please refresh and try again.';
        loading = false;
        return;
      }
    } else {
      console.log('reCAPTCHA is disabled, skipping token generation');
    }

    // Debug: Log the form data being sent
    console.log('Form submission debug:');
    for (let [key, value] of formData.entries()) {
      if (key === 'recaptcha_token') {
        console.log(`${key}: ${value.substring(0, 20)}...`);
      } else if (key === 'password') {
        console.log(`${key}: [${value.length} characters]`);
      } else {
        console.log(`${key}: ${value}`);
      }
    }

    try {
      console.log('Submitting to /login');
      const response = await fetch('/login', {
        method: 'POST',
        body: formData
      });

      if (response.ok) {
        const result = await response.json();

        if (result.requires_totp) {
          requiresTotp = true;
          loginError = '';
          loading = false;
          return;
        }

        loginSuccess = result.message || 'Login successful!';
        loginError = '';

        // Redirect to home page after successful login
        setTimeout(() => {
          window.location.href = '/';
        }, 1000);
      } else {
        const errorText = await response.text();
        loginError = errorText || 'Login failed';
        loginSuccess = '';
        requiresTotp = false;
      }
    } catch (err) {
      console.error('Login error:', err);
      loginError = 'Network error. Please try again.';
    } finally {
      loading = false;
    }
  }

  function handleGoogleLogin() {
		// For Google OAuth, we need to redirect directly to the auth endpoint
		// The backend will handle the OAuth flow and redirects
		window.location.href = '/auth?provider=google';
	}
</script>

<div class="auth-container">
  <div class="auth-card">
    <h1>Sign In</h1>

    {#if error}
      <div class="error-message">{error}</div>
    {/if}

    {#if loginError}
      <div class="error-message">{loginError}</div>
    {/if}

    {#if loginSuccess}
      <div class="success-message">{loginSuccess}</div>
    {/if}

    <!-- Google Authentication -->
    <div class="auth-section">
      <button class="google-btn" on:click={handleGoogleLogin}>
        <svg class="google-icon" viewBox="0 0 24 24">
          <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
          <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
          <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
          <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
        </svg>
        Continue with Google
      </button>
    </div>

    <div class="divider">
      <span>or</span>
    </div>

    <!-- Local Authentication Form -->
    <form class="auth-form" on:submit={handleLocalAuth}>
      <div class="form-group">
        <label for="email">Email</label>
        <input
          type="email"
          id="email"
          bind:value={email}
          required
          disabled={loading}
        />
      </div>

      <div class="form-group">
        <label for="password">Password</label>
        <input
          type="password"
          id="password"
          bind:value={password}
          required
          disabled={loading}
        />
      </div>
            {#if requiresTotp}
        <div class="form-group">
          <label for="totpCode">Two-Factor Authentication Code</label>
          <input
            type="text"
            id="totpCode"
            bind:value={totpCode}
            placeholder="000000"
            maxlength="6"
            required
            disabled={loading}
            class="totp-input"
          />
          <p class="totp-help">Enter the 6-digit code from your authenticator app</p>
        </div>
      {/if}

      <button type="submit" class="submit-btn" disabled={loading}>
        {loading ? 'Processing...' : 'Sign In'}
      </button>
    </form>

    <div class="contact-admin">
      <p>Need an account? Contact your system administrator.</p>
    </div>
  </div>
</div>

<style>
  .auth-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 20px;
    background-color: #f5f5f5;
  }

  .auth-card {
    background: white;
    padding: 40px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 400px;
  }

  h1 {
    text-align: center;
    margin-bottom: 30px;
    color: #333;
  }

  .error-message {
    background-color: #fee;
    color: #c33;
    padding: 10px;
    border-radius: 4px;
    margin-bottom: 20px;
    text-align: center;
  }

  .success-message {
    background-color: #efe;
    color: #393;
    padding: 10px;
    border-radius: 4px;
    margin-bottom: 20px;
    text-align: center;
  }

  .auth-section {
    margin-bottom: 20px;
  }

  .google-btn {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 12px;
    border: 1px solid #ddd;
    background: white;
    border-radius: 6px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.2s;
  }

  .google-btn:hover {
    background-color: #f9f9f9;
  }

  .google-icon {
    width: 20px;
    height: 20px;
  }

  .divider {
    text-align: center;
    margin: 20px 0;
    position: relative;
  }

  .divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #ddd;
  }

  .divider span {
    background: white;
    padding: 0 15px;
    color: #666;
  }

  .auth-form {
    margin-bottom: 20px;
  }

  .form-group {
    margin-bottom: 15px;
  }

  label {
    display: block;
    margin-bottom: 5px;
    color: #333;
    font-weight: 500;
  }

  input {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
    box-sizing: border-box;
  }

  input:focus {
    outline: none;
    border-color: #4285f4;
  }

  input:disabled {
    background-color: #f5f5f5;
    cursor: not-allowed;
  }

  .submit-btn {
    width: 100%;
    padding: 12px;
    background-color: #4285f4;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.2s;
  }

  .submit-btn:hover:not(:disabled) {
    background-color: #3367d6;
  }

  .submit-btn:disabled {
    background-color: #ccc;
    cursor: not-allowed;
  }

  .contact-admin {
    text-align: center;
    color: #666;
    margin-top: 20px;
  }

  .contact-admin p {
    margin: 0;
    font-size: 14px;
  }

.google-btn:hover {
    background-color: #357ae8;
  }

  .totp-input {
    margin-top: 10px;
  }

  .totp-help {
    font-size: 12px;
    color: #666;
    margin-top: 5px;
    text-align: center;
  }

</style>