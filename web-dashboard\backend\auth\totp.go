package auth

import (
	"bytes"
	"encoding/json"
	"fmt"
	"image/png"
	"net/http"
	"strings"
	"time"

	"web-dashboard/backend/database"
	"web-dashboard/backend/debug"
	"web-dashboard/backend/users"

	"github.com/pquerna/otp"
	"github.com/pquerna/otp/totp"
)

// TOTPSecret represents a TOTP secret for a user
type TOTPSecret struct {
	UserID    int    `json:"user_id"`
	Secret    string `json:"secret"`
	Enabled   bool   `json:"enabled"`
	CreatedAt time.Time `json:"created_at"`
}

// GenerateTOTPSecret generates a new TOTP secret for a user
func GenerateTOTPSecret(userEmail string) (*otp.Key, error) {
	// Get site URL from system settings
	siteURL := database.GetSettingString("site_url", "Web Dashboard")

	// If site URL is set, use it as issuer, otherwise use default
	issuer := "Web Dashboard"
	if siteURL != "" && siteURL != "Web Dashboard" {
		issuer = siteURL
	}

	key, err := totp.Generate(totp.GenerateOpts{
		Issuer:      issuer,
		AccountName: userEmail,
		SecretSize:  32,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to generate TOTP key: %v", err)
	}

	return key, nil
}

// StoreTOTPSecret stores the TOTP secret in the database
func StoreTOTPSecret(userID int, secret string) error {
	query := `INSERT INTO user_totp_secrets (user_id, secret, enabled, created_at) 
              VALUES ($1, $2, false, $3)
              ON CONFLICT (user_id) 
              DO UPDATE SET secret = $2, created_at = $3`

	_, err := database.DB.Exec(query, userID, secret, time.Now())
	if err != nil {
		return fmt.Errorf("failed to store TOTP secret: %v", err)
	}

	return nil
}

// GetTOTPSecret retrieves the TOTP secret for a user
func GetTOTPSecret(userID int) (*TOTPSecret, error) {
	query := `SELECT user_id, secret, enabled, created_at FROM user_totp_secrets WHERE user_id = $1`

	var totpSecret TOTPSecret
	err := database.DB.QueryRow(query, userID).Scan(
		&totpSecret.UserID, &totpSecret.Secret, &totpSecret.Enabled, &totpSecret.CreatedAt)

	if err != nil {
		if err.Error() == "sql: no rows in result set" {
			return nil, fmt.Errorf("TOTP secret not found")
		}
		return nil, fmt.Errorf("failed to get TOTP secret: %v", err)
	}

	return &totpSecret, nil
}

// EnableTOTP enables TOTP for a user after verification
func EnableTOTP(userID int) error {
	query := `UPDATE user_totp_secrets SET enabled = true WHERE user_id = $1`
	result, err := database.DB.Exec(query, userID)
	if err != nil {
		return fmt.Errorf("failed to enable TOTP: %v", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %v", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("TOTP secret not found for user")
	}

	return nil
}

// DisableTOTP disables TOTP for a user
func DisableTOTP(userID int) error {
	query := `DELETE FROM user_totp_secrets WHERE user_id = $1`
	result, err := database.DB.Exec(query, userID)
	if err != nil {
		return fmt.Errorf("failed to disable TOTP: %v", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %v", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("TOTP secret not found for user")
	}

	return nil
}

// ValidateTOTP validates a TOTP code for a user
func ValidateTOTP(userID int, code string) (bool, error) {
	totpSecret, err := GetTOTPSecret(userID)
	if err != nil {
		return false, err
	}

	if !totpSecret.Enabled {
		return false, fmt.Errorf("TOTP not enabled for user")
	}

	valid := totp.Validate(code, totpSecret.Secret)
	return valid, nil
}

// SetupTOTPHandler handles TOTP setup requests
func SetupTOTPHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != "POST" {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Get current user
	userEmail, err := GetCurrentUserEmail(r)
	if err != nil {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	user, err := users.GetUserByEmail(userEmail)
	if err != nil {
		http.Error(w, "User not found", http.StatusNotFound)
		return
	}

	// Generate TOTP secret
	key, err := GenerateTOTPSecret(userEmail)
	if err != nil {
		debug.Error("Failed to generate TOTP secret: %v", err)
		http.Error(w, "Failed to generate TOTP secret", http.StatusInternalServerError)
		return
	}

	// Store the secret in database (disabled by default)
	err = StoreTOTPSecret(user.ID, key.Secret())
	if err != nil {
		debug.Error("Failed to store TOTP secret: %v", err)
		http.Error(w, "Failed to store TOTP secret", http.StatusInternalServerError)
		return
	}

	// Generate QR code
	var buf bytes.Buffer
	img, err := key.Image(200, 200)
	if err != nil {
		debug.Error("Failed to generate QR code: %v", err)
		http.Error(w, "Failed to generate QR code", http.StatusInternalServerError)
		return
	}

	png.Encode(&buf, img)

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"success":    true,
		"secret":     key.Secret(),
		"qr_code":    buf.Bytes(),
		"manual_key": key.Secret(),
		"url":        key.URL(),
	})
}

// VerifyTOTPSetupHandler verifies TOTP setup and enables it
func VerifyTOTPSetupHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != "POST" {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Get current user
	userEmail, err := GetCurrentUserEmail(r)
	if err != nil {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	user, err := users.GetUserByEmail(userEmail)
	if err != nil {
		http.Error(w, "User not found", http.StatusNotFound)
		return
	}

	code := strings.TrimSpace(r.FormValue("code"))
	if code == "" {
		http.Error(w, "TOTP code is required", http.StatusBadRequest)
		return
	}

	// Get the stored secret
	totpSecret, err := GetTOTPSecret(user.ID)
	if err != nil {
		http.Error(w, "TOTP secret not found", http.StatusNotFound)
		return
	}

	// Validate the TOTP code
	valid := totp.Validate(code, totpSecret.Secret)
	if !valid {
		http.Error(w, "Invalid TOTP code", http.StatusBadRequest)
		return
	}

	// Enable TOTP for the user
	err = EnableTOTP(user.ID)
	if err != nil {
		debug.Error("Failed to enable TOTP: %v", err)
		http.Error(w, "Failed to enable TOTP", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"success": true,
		"message": "TOTP enabled successfully",
	})
}

// DisableTOTPHandler disables TOTP for a user
func DisableTOTPHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != "POST" {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Get current user
	userEmail, err := GetCurrentUserEmail(r)
	if err != nil {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	user, err := users.GetUserByEmail(userEmail)
	if err != nil {
		http.Error(w, "User not found", http.StatusNotFound)
		return
	}

	// Check if user is a Google OAuth user
	if user.LastLoginMethod == "google" {
		// Google OAuth users don't have a traditional password to verify
		// We can allow them to disable TOTP since they're already authenticated
		debug.Info("Allowing Google OAuth user %s to disable TOTP without password verification", userEmail)
	} else {
		// For local users, require password verification
		currentPassword := r.FormValue("current_password")
		if currentPassword == "" {
			http.Error(w, "Current password is required", http.StatusBadRequest)
			return
		}

		// Verify current password
		userWithPassword, err := users.GetUserByEmailWithPassword(userEmail)
		if err != nil {
			http.Error(w, "Failed to verify user", http.StatusInternalServerError)
			return
		}

		if !VerifyPassword(currentPassword, userWithPassword.PasswordHash) {
			http.Error(w, "Invalid password", http.StatusUnauthorized)
			return
		}
	}

	// Disable TOTP
	err = DisableTOTP(user.ID)
	if err != nil {
		debug.Error("Failed to disable TOTP: %v", err)
		http.Error(w, "Failed to disable TOTP", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"success": true,
		"message": "TOTP disabled successfully",
	})
}

// GetTOTPStatusHandler returns the TOTP status for the current user
func GetTOTPStatusHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != "GET" {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Get current user
	userEmail, err := GetCurrentUserEmail(r)
	if err != nil {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	user, err := users.GetUserByEmail(userEmail)
	if err != nil {
		http.Error(w, "User not found", http.StatusNotFound)
		return
	}

	// Check if TOTP is enabled
	totpSecret, err := GetTOTPSecret(user.ID)
	enabled := false
	if err == nil && totpSecret.Enabled {
		enabled = true
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"enabled": enabled,
	})
}

// RequireTOTP checks if a user has TOTP enabled and requires it
func RequireTOTP(userID int) (bool, error) {
	totpSecret, err := GetTOTPSecret(userID)
	if err != nil {
		// If no TOTP secret found, TOTP is not required
		return false, nil
	}

	return totpSecret.Enabled, nil
}