<script>
	export let userInfo = null;

	let loading = true;
	let error = null;
	let hasPermission = false;

	// Redis stats
	let redisStats = null;
	let loadingStats = false;

	// Keys management
	let keys = [];
	let loadingKeys = false;
	let keyPattern = '*';
	let selectedKey = null;
	let keyData = null;
	let loadingKeyData = false;

	// Sessions management
	let sessions = [];
	let loadingSessions = false;

	// Reactive statement that runs when userInfo prop changes
	$: {
		if (userInfo) {
			initializeComponent();
		} else {
			loading = false;
			error = 'User information not available';
		}
	}

	async function initializeComponent() {
		try {
			// Check if user has admin permissions by trying to access Redis stats
			await checkAdminPermissions();

			if (hasPermission) {
				await loadRedisStats();
				await loadKeys();
				await loadSessions();
			}
		} catch (err) {
			error = 'Failed to initialize Redis interface';
			console.error('Error initializing Redis interface:', err);
		} finally {
			loading = false;
		}
	}

	async function checkAdminPermissions() {
		console.log('RedisManager: Checking admin permissions...');
		try {
			const response = await fetch('/api/redis/stats');
			console.log('RedisManager: Permission check response status:', response.status);
			if (response.ok) {
				hasPermission = true;
				console.log('RedisManager: Admin permissions granted');
			} else if (response.status === 403) {
				hasPermission = false;
				error = 'Admin permission required to access Redis data';
				console.log('RedisManager: Access denied - insufficient permissions');
			} else {
				hasPermission = false;
				error = 'Failed to verify Redis access permissions';
				console.log('RedisManager: Permission check failed with status:', response.status);
			}
		} catch (err) {
			hasPermission = false;
			error = 'Failed to check Redis permissions';
			console.error('RedisManager: Error checking Redis permissions:', err);
		}
	}

	async function loadRedisStats() {
		loadingStats = true;
		console.log('RedisManager: Loading Redis stats...');
		try {
			const response = await fetch('/api/redis/stats');
			console.log('RedisManager: Stats response status:', response.status);
			if (response.ok) {
				redisStats = await response.json();
				console.log('RedisManager: Redis stats loaded:', redisStats);
			} else {
				throw new Error(`HTTP ${response.status}`);
			}
		} catch (err) {
			console.error('RedisManager: Failed to load Redis stats:', err);
			error = 'Failed to load Redis statistics';
		} finally {
			loadingStats = false;
		}
	}

	async function loadKeys() {
		loadingKeys = true;
		try {
			const url = keyPattern ? `/api/redis/keys?pattern=${encodeURIComponent(keyPattern)}` : '/api/redis/keys';
			const response = await fetch(url);
			if (response.ok) {
				keys = await response.json();
			} else {
				console.error('Failed to load keys, status:', response.status);
			}
		} catch (err) {
			console.error('Failed to load keys:', err);
		} finally {
			loadingKeys = false;
		}
	}

	async function loadKeyData(key) {
		loadingKeyData = true;
		selectedKey = key;
		try {
			const response = await fetch(`/api/redis/key/${encodeURIComponent(key)}`);
			if (response.ok) {
				keyData = await response.json();
			} else {
				keyData = null;
				if (response.status === 404) {
					alert('Key not found');
				}
			}
		} catch (err) {
			console.error('Failed to load key data:', err);
			keyData = null;
		} finally {
			loadingKeyData = false;
		}
	}

	async function deleteKey(key) {
		if (!confirm(`Are you sure you want to delete the key "${key}"?`)) {
			return;
		}

		try {
			const response = await fetch(`/api/redis/key/${encodeURIComponent(key)}`, {
				method: 'DELETE'
			});
			if (response.ok) {
				await loadKeys();
				await loadRedisStats();
				if (selectedKey === key) {
					selectedKey = null;
					keyData = null;
				}
			}
		} catch (err) {
			console.error('Failed to delete key:', err);
			alert('Failed to delete key');
		}
	}

	async function loadSessions() {
		loadingSessions = true;
		try {
			const response = await fetch('/api/redis/sessions');
			if (response.ok) {
				sessions = await response.json();
			}
		} catch (err) {
			console.error('Failed to load sessions:', err);
		} finally {
			loadingSessions = false;
		}
	}

	async function deleteSession(sessionId) {
		if (!confirm('Are you sure you want to delete this session? The user will be logged out.')) {
			return;
		}

		try {
			const response = await fetch(`/api/redis/session/${encodeURIComponent(sessionId)}`, {
				method: 'DELETE'
			});
			if (response.ok) {
				await loadSessions();
				await loadRedisStats();
			}
		} catch (err) {
			console.error('Failed to delete session:', err);
			alert('Failed to delete session');
		}
	}

	async function flushRedis() {
		if (!confirm('Are you sure you want to flush ALL Redis data? This will log out all users and cannot be undone!')) {
			return;
		}

		try {
			const response = await fetch('/api/redis/flush', {
				method: 'DELETE'
			});
			if (response.ok) {
				await loadRedisStats();
				await loadKeys();
				await loadSessions();
				selectedKey = null;
				keyData = null;
				alert('Redis data flushed successfully');
			}
		} catch (err) {
			console.error('Failed to flush Redis:', err);
			alert('Failed to flush Redis data');
		}
	}

	function formatValue(value) {
		if (typeof value === 'string') {
			try {
				// Try to parse as JSON for pretty printing
				const parsed = JSON.parse(value);
				return JSON.stringify(parsed, null, 2);
			} catch {
				return value;
			}
		}
		return JSON.stringify(value, null, 2);
	}

	function formatTimestamp(timestamp) {
		return new Date(timestamp * 1000).toLocaleString();
	}

	function formatTTL(ttl) {
		if (ttl === -1) return 'No expiration';
		if (ttl === -2) return 'Key does not exist';
		if (ttl < 60) return `${ttl}s`;
		if (ttl < 3600) return `${Math.floor(ttl / 60)}m ${ttl % 60}s`;
		const hours = Math.floor(ttl / 3600);
		const minutes = Math.floor((ttl % 3600) / 60);
		return `${hours}h ${minutes}m`;
	}
</script>

<div class="redis-manager-container">
	<h1>Redis Management</h1>

	{#if loading}
		<div class="loading">Loading Redis interface...</div>
	{:else if error}
		<div class="error">
			<p>{error}</p>
			<button on:click={() => window.location.reload()}>Retry</button>
		</div>
	{:else if !hasPermission}
		<div class="access-denied">
			<h2>Access Denied</h2>
			<p>You don't have permission to access Redis management.</p>
			<p>Only super administrators can manage Redis data.</p>
		</div>
	{:else}
		<!-- Redis Stats Section -->
		<div class="redis-section">
			<div class="section-header">
				<h2>Redis Statistics</h2>
				<button class="refresh-btn" on:click={loadRedisStats} disabled={loadingStats}>
					{loadingStats ? 'Loading...' : 'Refresh'}
				</button>
			</div>

			{#if redisStats}
				<div class="stats-grid">
					<div class="stat-item">
						<span class="stat-label">Memory Used:</span>
						<span class="stat-value">{redisStats.used_memory || 'N/A'}</span>
					</div>
					<div class="stat-item">
						<span class="stat-label">Connected Clients:</span>
						<span class="stat-value">{redisStats.connected_clients || 0}</span>
					</div>
					<div class="stat-item">
						<span class="stat-label">Active Sessions:</span>
						<span class="stat-value">{redisStats.sessions ? redisStats.sessions.length : 0}</span>
					</div>
				</div>
			{/if}
		</div>

		<!-- Sessions Management Section -->
		<div class="redis-section">
			<div class="section-header">
				<h2>Active Sessions</h2>
				<button class="refresh-btn" on:click={loadSessions} disabled={loadingSessions}>
					{loadingSessions ? 'Loading...' : 'Refresh'}
				</button>
			</div>

			{#if loadingSessions}
				<div class="loading">Loading sessions...</div>
			{:else if !sessions || sessions.length === 0}
				<p>No active sessions found</p>
			{:else}
				<div class="sessions-table">
					<table>
						<thead>
							<tr>
								<th>Session ID</th>
								<th>Email</th>
								<th>Auth Type</th>
								<th>IP Address</th>
								<th>Created</th>
								<th>Last Access</th>
								<th>Permissions</th>
								<th>Actions</th>
							</tr>
						</thead>
						<tbody>
							{#each sessions || [] as session}
								<tr>
									<td class="session-id">{session.session_id ? session.session_id.substring(0, 8) + '...' : 'N/A'}</td>
									<td>{session.email || 'N/A'}</td>
									<td>{session.auth_type || 'N/A'}</td>
									<td class="ip-address">{session.ip_address || 'N/A'}</td>
									<td>{session.created_at ? formatTimestamp(session.created_at) : 'N/A'}</td>
									<td>{session.last_access ? formatTimestamp(session.last_access) : 'N/A'}</td>
									<td>
										<div class="permissions">
											{#each (session.permissions || []) as perm}
												<span class="permission-tag">{perm}</span>
											{/each}
										</div>
									</td>
									<td>
										<button class="action-btn delete" on:click={() => deleteSession(session.session_id)}>
											Delete
										</button>
									</td>
								</tr>
							{/each}
						</tbody>
					</table>
				</div>
			{/if}
		</div>

		<!-- Keys Management Section -->
		<div class="redis-section">
			<div class="section-header">
				<h2>Keys Management</h2>
				<div class="key-controls">
					<input
						type="text"
						bind:value={keyPattern}
						placeholder="Key pattern (e.g., session:*, user:*)"
						class="pattern-input"
					/>
					<button class="refresh-btn" on:click={loadKeys} disabled={loadingKeys}>
						{loadingKeys ? 'Loading...' : 'Search'}
					</button>
				</div>
			</div>

			<div class="keys-container">
				<div class="keys-list">
					{#if loadingKeys}
						<div class="loading">Loading keys...</div>
					{:else if !keys || keys.length === 0}
						<p>No keys found matching pattern "{keyPattern}"</p>
					{:else}
						<div class="keys-table">
							<table>
								<thead>
									<tr>
										<th>Key</th>
										<th>Type</th>
										<th>TTL</th>
										<th>Actions</th>
									</tr>
								</thead>
								<tbody>
									{#each keys || [] as key}
										<tr class:selected={selectedKey === (key ? key.key : null)}>
											<td class="key-name" on:click={() => key && loadKeyData(key.key)}>
												{key ? key.key : 'N/A'}
											</td>
											<td>{key ? key.type : 'N/A'}</td>
											<td>{key ? formatTTL(key.ttl) : 'N/A'}</td>
											<td>
												<button class="action-btn view" on:click={() => key && loadKeyData(key.key)}>
													View
												</button>
												<button class="action-btn delete" on:click={() => key && deleteKey(key.key)}>
													Delete
												</button>
											</td>
										</tr>
									{/each}
								</tbody>
							</table>
						</div>
					{/if}
				</div>

				{#if selectedKey}
					<div class="key-details">
						<div class="key-details-header">
							<h3>Key Details: {selectedKey}</h3>
							<button class="close-btn" on:click={() => { selectedKey = null; keyData = null; }}>×</button>
						</div>

						{#if loadingKeyData}
							<div class="loading">Loading key data...</div>
						{:else if keyData}
							<div class="key-info">
								<div class="info-row">
									<span class="info-label">Type:</span>
									<span>{keyData.type}</span>
								</div>
								<div class="info-row">
									<span class="info-label">TTL:</span>
									<span>{formatTTL(keyData.ttl)}</span>
								</div>
							</div>
							<div class="key-value">
								<span class="info-label">Value:</span>
								<pre class="value-display">{formatValue(keyData.value)}</pre>
							</div>
						{:else}
							<p>Failed to load key data</p>
						{/if}
					</div>
				{/if}
			</div>
		</div>

		<!-- Danger Zone -->
		<div class="redis-section danger-zone">
			<div class="section-header">
				<h2>Danger Zone</h2>
			</div>
			<div class="danger-content">
				<p class="warning-text">These actions are irreversible and will affect all users.</p>
				<button class="danger-btn" on:click={flushRedis}>
					Flush All Redis Data
				</button>
			</div>
		</div>
	{/if}
</div>

<style>
	.redis-manager-container {
		max-width: 1200px;
		margin: 2rem auto;
		padding: 0 1rem;
	}

	h1 {
		color: #333;
		margin-bottom: 2rem;
		text-align: center;
	}

	.loading {
		text-align: center;
		padding: 2rem;
		color: #666;
	}

	.error {
		text-align: center;
		padding: 2rem;
		color: #dc3545;
	}

	.access-denied {
		text-align: center;
		padding: 3rem;
		background: white;
		border-radius: 8px;
		box-shadow: 0 2px 10px rgba(0,0,0,0.1);
	}

	.redis-section {
		background: white;
		border-radius: 8px;
		box-shadow: 0 2px 10px rgba(0,0,0,0.1);
		margin-bottom: 2rem;
		overflow: hidden;
	}

	.section-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 1.5rem 2rem;
		background-color: #f8f9fa;
		border-bottom: 1px solid #e9ecef;
	}

	.section-header h2 {
		margin: 0;
		color: #333;
	}

	.key-controls {
		display: flex;
		gap: 0.5rem;
		align-items: center;
	}

	.pattern-input {
		padding: 0.5rem;
		border: 1px solid #ddd;
		border-radius: 4px;
		min-width: 250px;
	}

	.refresh-btn {
		padding: 0.5rem 1rem;
		background-color: #007bff;
		color: white;
		border: none;
		border-radius: 4px;
		cursor: pointer;
	}

	.refresh-btn:hover:not(:disabled) {
		background-color: #0056b3;
	}

	.refresh-btn:disabled {
		background-color: #6c757d;
		cursor: not-allowed;
	}

	.stats-grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
		gap: 1rem;
		padding: 2rem;
	}

	.stat-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 1rem;
		background-color: #f8f9fa;
		border-radius: 6px;
		border: 1px solid #e9ecef;
	}

	.stat-label {
		font-weight: 600;
		color: #555;
	}

	.stat-value {
		font-size: 1.2rem;
		font-weight: 700;
		color: #007bff;
	}

	

	.keys-container {
		display: grid;
		grid-template-columns: 1fr auto;
		gap: 1rem;
		padding: 2rem;
	}

	.keys-list {
		min-width: 0;
	}

	.keys-table table, .sessions-table table {
		width: 100%;
		border-collapse: collapse;
	}

	.keys-table th, .keys-table td,
	.sessions-table th, .sessions-table td {
		padding: 0.75rem;
		text-align: left;
		border-bottom: 1px solid #ddd;
	}

	.keys-table th, .sessions-table th {
		background-color: #f8f9fa;
		font-weight: 600;
		color: #555;
	}

	.keys-table tbody tr:hover {
		background-color: #f8f9fa;
	}

	.keys-table tbody tr.selected {
		background-color: #e3f2fd;
	}

	.key-name {
		cursor: pointer;
		color: #007bff;
		font-family: monospace;
	}

	.key-name:hover {
		text-decoration: underline;
	}

	.session-id {
		font-family: monospace;
		font-size: 0.875rem;
	}

	.permissions {
		display: flex;
		flex-wrap: wrap;
		gap: 0.25rem;
	}

	.permission-tag {
		background-color: #e9ecef;
		color: #495057;
		padding: 0.25rem 0.5rem;
		border-radius: 3px;
		font-size: 0.75rem;
		font-weight: 500;
	}

	.action-btn {
		padding: 0.375rem 0.75rem;
		border: none;
		border-radius: 4px;
		cursor: pointer;
		font-size: 0.875rem;
		margin-right: 0.5rem;
	}

	.action-btn.view {
		background-color: #17a2b8;
		color: white;
	}

	.action-btn.view:hover {
		background-color: #138496;
	}

	.action-btn.delete {
		background-color: #dc3545;
		color: white;
	}

	.action-btn.delete:hover {
		background-color: #c82333;
	}

	.key-details {
		width: 400px;
		background-color: #f8f9fa;
		border: 1px solid #e9ecef;
		border-radius: 6px;
		padding: 1rem;
	}

	.key-details-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 1rem;
	}

	.key-details-header h3 {
		margin: 0;
		color: #333;
		font-size: 1rem;
		word-break: break-all;
	}

	.close-btn {
		background: none;
		border: none;
		font-size: 1.5rem;
		cursor: pointer;
		color: #6c757d;
		padding: 0;
		width: 24px;
		height: 24px;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.close-btn:hover {
		color: #dc3545;
	}

	.key-info {
		margin-bottom: 1rem;
	}

	.info-row {
		display: flex;
		justify-content: space-between;
		margin-bottom: 0.5rem;
	}

	.info-label {
		font-weight: 600;
		color: #555;
	}

	.key-value .info-label {
		display: block;
		font-weight: 600;
		color: #555;
		margin-bottom: 0.5rem;
	}

	.value-display {
		background-color: white;
		border: 1px solid #ddd;
		border-radius: 4px;
		padding: 1rem;
		max-height: 300px;
		overflow: auto;
		font-family: 'Courier New', monospace;
		font-size: 0.875rem;
		white-space: pre-wrap;
		word-break: break-all;
	}

	.danger-zone {
		border: 2px solid #dc3545;
	}

	.danger-zone .section-header {
		background-color: #f8d7da;
		color: #721c24;
	}

	.danger-content {
		padding: 2rem;
	}

	.warning-text {
		color: #721c24;
		margin: 0 0 1rem 0;
		font-weight: 500;
	}

	.danger-btn {
		margin: 0;
		padding: 0.75rem 1.5rem;
		background-color: #dc3545;
		color: white;
		border: none;
		border-radius: 4px;
		cursor: pointer;
		font-weight: 600;
	}

	.danger-btn:hover {
		background-color: #c82333;
	}

  .email-cell {
    max-width: 200px;
    word-break: break-word;
  }

  .ip-address {
    font-family: monospace;
    font-size: 0.9em;
    color: #666;
  }

	@media (max-width: 768px) {
		.keys-container {
			grid-template-columns: 1fr;
		}

		.key-details {
			width: 100%;
		}

		.section-header {
			flex-direction: column;
			gap: 1rem;
			align-items: stretch;
		}

		.key-controls {
			justify-content: stretch;
		}

		.pattern-input {
			min-width: 0;
			flex: 1;
		}

		.stats-grid {
			grid-template-columns: 1fr;
		}
	}
</style>