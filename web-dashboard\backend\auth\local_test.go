
package auth

import (
	"net/http"
	"net/http/httptest"
	"net/url"
	"strings"
	"testing"
	"time"

	"web-dashboard/backend/database"
	"web-dashboard/backend/session"
	"web-dashboard/backend/users"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestHashPassword(t *testing.T) {
	tests := []struct {
		name     string
		password string
	}{
		{
			name:     "simple password",
			password: "password123",
		},
		{
			name:     "complex password",
			password: "MyC0mpl3x!P@ssw0rd#2023",
		},
		{
			name:     "empty password",
			password: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			hash, err := HashPassword(tt.password)
			require.NoError(t, err)
			assert.NotEmpty(t, hash)
			assert.Contains(t, hash, ":")

			// Verify the hash can be used to verify the password
			isValid := VerifyPassword(tt.password, hash)
			assert.True(t, isValid)

			// Verify wrong password fails
			if tt.password != "" {
				isValid = VerifyPassword(tt.password+"wrong", hash)
				assert.False(t, isValid)
			}
		})
	}
}

func TestVerifyPassword(t *testing.T) {
	password := "testpassword123"
	hash, err := HashPassword(password)
	require.NoError(t, err)

	tests := []struct {
		name     string
		password string
		hash     string
		expected bool
	}{
		{
			name:     "correct password",
			password: password,
			hash:     hash,
			expected: true,
		},
		{
			name:     "incorrect password",
			password: "wrongpassword",
			hash:     hash,
			expected: false,
		},
		{
			name:     "invalid hash format",
			password: password,
			hash:     "invalid-hash",
			expected: false,
		},
		{
			name:     "empty password",
			password: "",
			hash:     hash,
			expected: false,
		},
		{
			name:     "empty hash",
			password: password,
			hash:     "",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := VerifyPassword(tt.password, tt.hash)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestLocalLoginHandler(t *testing.T) {
	// Initialize Redis session manager for testing
	err := session.InitRedis("redis://localhost:6379")
	if err != nil {
		t.Skip("Redis not available for testing, skipping test")
	}
	
	// Cleanup any existing test user first
	userEmail := "<EMAIL>"
	database.DB.Exec("DELETE FROM users WHERE email = $1", userEmail)
	
	// Create a test user
	password := "password123"
	hashedPassword, _ := HashPassword(password)
	userID, err := users.CreateUser(userEmail, password, true, true)
	require.NoError(t, err)

	// Update user with hashed password
	database.DB.Exec("UPDATE users SET password_hash = $1 WHERE id = $2", hashedPassword, userID)

	tests := []struct {
		name           string
		method         string
		email          string
		password       string
		expectedStatus int
		expectCookie   bool
	}{
		{
			name:           "successful login",
			method:         "POST",
			email:          userEmail,
			password:       password,
			expectedStatus: http.StatusOK,
			expectCookie:   true,
		},
		{
			name:           "invalid password",
			method:         "POST",
			email:          userEmail,
			password:       "wrongpassword",
			expectedStatus: http.StatusUnauthorized,
			expectCookie:   false,
		},
		{
			name:           "invalid email",
			method:         "POST",
			email:          "<EMAIL>",
			password:       password,
			expectedStatus: http.StatusUnauthorized,
			expectCookie:   false,
		},
		{
			name:           "missing email",
			method:         "POST",
			email:          "",
			password:       password,
			expectedStatus: http.StatusBadRequest,
			expectCookie:   false,
		},
		{
			name:           "missing password",
			method:         "POST",
			email:          userEmail,
			password:       "",
			expectedStatus: http.StatusBadRequest,
			expectCookie:   false,
		},
		{
			name:           "GET method not allowed",
			method:         "GET",
			email:          userEmail,
			password:       password,
			expectedStatus: http.StatusMethodNotAllowed,
			expectCookie:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			form := url.Values{}
			form.Add("email", tt.email)
			form.Add("password", tt.password)

			req := httptest.NewRequest(tt.method, "/login", strings.NewReader(form.Encode()))
			req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

			rr := httptest.NewRecorder()
			LocalLoginHandler(rr, req)

			assert.Equal(t, tt.expectedStatus, rr.Code)

			if tt.expectCookie {
				cookies := rr.Result().Cookies()
				found := false
				for _, cookie := range cookies {
					if cookie.Name == "session_token" && cookie.Value != "" {
						found = true
						break
					}
				}
				assert.True(t, found, "Expected session_token cookie to be set")
			}
		})
	}

	// Cleanup
	database.DB.Exec("DELETE FROM users WHERE email = $1", userEmail)
}

func TestChangePasswordHandler(t *testing.T) {
	// Initialize Redis session manager for testing
	err := session.InitRedis("redis://localhost:6379")
	if err != nil {
		t.Skip("Redis not available for testing, skipping test")
	}
	
	// Cleanup any existing test user first
	userEmail := "<EMAIL>"
	database.DB.Exec("DELETE FROM users WHERE email = $1", userEmail)
	
	// Create a test user
	currentPassword := "oldpassword123"
	hashedPassword, _ := HashPassword(currentPassword)
	userID, err := users.CreateUser(userEmail, currentPassword, true, true)
	require.NoError(t, err)

	// Update user with hashed password
	database.DB.Exec("UPDATE users SET password_hash = $1 WHERE id = $2", hashedPassword, userID)

	// Create session for the user using Redis session manager
	sessionToken := "changepwd-session"
	permissions := []string{"Read Access"}
	clientIP := "127.0.0.1"
	err = session.Manager.CreateSession(sessionToken, userID, userEmail, "local", permissions, clientIP)
	require.NoError(t, err)

	tests := []struct {
		name            string
		method          string
		currentPassword string
		newPassword     string
		sessionToken    string
		expectedStatus  int
	}{
		{
			name:            "successful password change",
			method:          "POST",
			currentPassword: currentPassword,
			newPassword:     "newpassword123",
			sessionToken:    sessionToken,
			expectedStatus:  http.StatusOK,
		},
		{
			name:            "incorrect current password",
			method:          "POST",
			currentPassword: "wrongpassword",
			newPassword:     "newpassword123",
			sessionToken:    sessionToken,
			expectedStatus:  http.StatusUnauthorized,
		},
		{
			name:            "missing current password",
			method:          "POST",
			currentPassword: "",
			newPassword:     "newpassword123",
			sessionToken:    sessionToken,
			expectedStatus:  http.StatusBadRequest,
		},
		{
			name:            "missing new password",
			method:          "POST",
			currentPassword: currentPassword,
			newPassword:     "",
			sessionToken:    sessionToken,
			expectedStatus:  http.StatusBadRequest,
		},
		{
			name:            "no session",
			method:          "POST",
			currentPassword: currentPassword,
			newPassword:     "newpassword123",
			sessionToken:    "",
			expectedStatus:  http.StatusUnauthorized,
		},
		{
			name:            "GET method not allowed",
			method:          "GET",
			currentPassword: currentPassword,
			newPassword:     "newpassword123",
			sessionToken:    sessionToken,
			expectedStatus:  http.StatusMethodNotAllowed,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			form := url.Values{}
			form.Add("current_password", tt.currentPassword)
			form.Add("new_password", tt.newPassword)

			req := httptest.NewRequest(tt.method, "/change-password", strings.NewReader(form.Encode()))
			req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

			if tt.sessionToken != "" {
				cookie := &http.Cookie{
					Name:  "session_token",
					Value: tt.sessionToken,
				}
				req.AddCookie(cookie)
			}

			rr := httptest.NewRecorder()
			ChangePasswordHandler(rr, req)

			assert.Equal(t, tt.expectedStatus, rr.Code)
		})
	}

	// Cleanup
	session.Manager.DeleteSession(sessionToken)
	database.DB.Exec("DELETE FROM users WHERE email = $1", userEmail)
}

func TestGenerateSecureToken(t *testing.T) {
	token1, err1 := generateSecureToken()
	token2, err2 := generateSecureToken()

	assert.NoError(t, err1)
	assert.NoError(t, err2)
	assert.NotEmpty(t, token1)
	assert.NotEmpty(t, token2)
	assert.NotEqual(t, token1, token2) // Tokens should be unique
	assert.Equal(t, 64, len(token1))   // 32 bytes = 64 hex characters
	assert.Equal(t, 64, len(token2))
}

func TestGetClientIP(t *testing.T) {
	tests := []struct {
		name           string
		remoteAddr     string
		xForwardedFor  string
		xRealIP        string
		expectedIP     string
	}{
		{
			name:           "X-Forwarded-For header single IP",
			remoteAddr:     "***********:12345",
			xForwardedFor:  "***********",
			expectedIP:     "***********",
		},
		{
			name:           "X-Forwarded-For header multiple IPs",
			remoteAddr:     "***********:12345",
			xForwardedFor:  "***********, ***********, ********",
			expectedIP:     "***********",
		},
		{
			name:           "X-Real-IP header",
			remoteAddr:     "***********:12345",
			xRealIP:        "***********",
			expectedIP:     "***********",
		},
		{
			name:           "RemoteAddr fallback",
			remoteAddr:     "***********:12345",
			expectedIP:     "***********",
		},
		{
			name:           "RemoteAddr without port",
			remoteAddr:     "***********",
			expectedIP:     "***********",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := httptest.NewRequest("GET", "/", nil)
			req.RemoteAddr = tt.remoteAddr

			if tt.xForwardedFor != "" {
				req.Header.Set("X-Forwarded-For", tt.xForwardedFor)
			}
			if tt.xRealIP != "" {
				req.Header.Set("X-Real-IP", tt.xRealIP)
			}

			ip := getClientIP(req)
			assert.Equal(t, tt.expectedIP, ip)
		})
	}
}

func TestBruteForceProtection(t *testing.T) {
	protection := &BruteForceProtection{
		attempts: make(map[string]*AttemptInfo),
	}

	clientIP := "*************"

	// Test initial request should be allowed
	allowed, waitTime := protection.checkResetTokenRateLimit(clientIP)
	assert.True(t, allowed)
	assert.Equal(t, time.Duration(0), waitTime)

	// Test recording failed attempts
	for i := 0; i < 3; i++ {
		protection.recordResetTokenAttempt(clientIP, false)
	}

	// After 3 failed attempts, should be blocked for 5 minutes
	allowed, waitTime = protection.checkResetTokenRateLimit(clientIP)
	assert.False(t, allowed)
	assert.True(t, waitTime > time.Minute*4) // Should be close to 5 minutes

	// Test successful attempt resets counter
	protection.recordResetTokenAttempt(clientIP, true)
	allowed, waitTime = protection.checkResetTokenRateLimit(clientIP)
	assert.True(t, allowed)
	assert.Equal(t, time.Duration(0), waitTime)
}

func TestGetUserLoginInfo(t *testing.T) {
	// Create a test user
	userEmail := "<EMAIL>"
	password := "password123"
	_, err := users.CreateUser(userEmail, password, true, true)
	require.NoError(t, err)

	tests := []struct {
		name        string
		email       string
		expectError bool
	}{
		{
			name:        "valid user email",
			email:       userEmail,
			expectError: false,
		},
		{
			name:        "invalid user email",
			email:       "<EMAIL>",
			expectError: true,
		},
		{
			name:        "empty email",
			email:       "",
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			userInfo, err := GetUserLoginInfo(tt.email)

			if tt.expectError {
				assert.Error(t, err)
				assert.Nil(t, userInfo)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, userInfo)
				assert.Equal(t, tt.email, userInfo["email"])
				assert.Contains(t, userInfo, "id")
				assert.Contains(t, userInfo, "ip_history")
			}
		})
	}

	// Cleanup
	database.DB.Exec("DELETE FROM users WHERE email = $1", userEmail)
}
