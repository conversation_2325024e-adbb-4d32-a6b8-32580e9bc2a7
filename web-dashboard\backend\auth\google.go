package auth

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"strings"

	"web-dashboard/backend/database"
	"web-dashboard/backend/debug"
	"web-dashboard/backend/session"
	"web-dashboard/backend/users"

	"golang.org/x/oauth2"
	"golang.org/x/oauth2/google"
	v2 "google.golang.org/api/oauth2/v2"
	"google.golang.org/api/option"
)

var config *oauth2.Config

// InitGoogleOAuth initializes the Google OAuth configuration
func InitGoogleOAuth() {
	// Initialize OAuth config with environment variables
	clientID := os.Getenv("GOOGLE_CLIENT_ID")
	clientSecret := os.Getenv("GOOGLE_CLIENT_SECRET")

	// Get site URL from system settings, fallback to environment variables
	siteURL := database.GetSettingString("site_url", "")
	var redirectURL string

	if siteURL != "" {
		// Use the configured site URL from system settings
		redirectURL = strings.TrimSuffix(siteURL, "/") + "/callback"
	} else {
		// Fallback for local development
		redirectURL = "http://localhost:5000/callback"
	}

	debug.Debug("OAuth redirect URL configured as: %s", redirectURL)

	config = &oauth2.Config{
		ClientID:     clientID,
		ClientSecret: clientSecret,
		RedirectURL:  redirectURL,
		Scopes:       []string{"email", "profile"},
		Endpoint:     google.Endpoint,
	}
}

// HandleGoogleOAuth handles the Google OAuth flow initiation
func HandleGoogleOAuth(w http.ResponseWriter, r *http.Request) {
	// Check if OAuth is properly configured
	if config == nil || config.ClientID == "" || config.ClientSecret == "" {
		http.Error(w, "OAuth not configured. Please set GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET environment variables.", http.StatusInternalServerError)
		return
	}

	debug.Debug("Using configured OAuth redirect URL: %s", config.RedirectURL)

	// Start Google OAuth flow
	url := config.AuthCodeURL("state")
  debug.Debug("Redirecting to Google OAuth URL: %s", url)
	http.Redirect(w, r, url, http.StatusFound)
}

// GoogleCallbackHandler handles the Google OAuth callback
func GoogleCallbackHandler(w http.ResponseWriter, r *http.Request) {
						 debug.Debug("GoogleCallbackHandler called with URL: %s", r.URL.String())

	// Check if OAuth is properly configured before proceeding
	if config == nil || config.ClientID == "" || config.ClientSecret == "" {
		debug.Debug("OAuth not properly configured in callback")
		http.Error(w, "OAuth not configured properly", http.StatusInternalServerError)
		return
	}

	// Check for OAuth error response from Google
	if errCode := r.URL.Query().Get("error"); errCode != "" {
		errDescription := r.URL.Query().Get("error_description")
		debug.Error("OAuth error: %s - %s", errCode, errDescription)
		http.Error(w, fmt.Sprintf("OAuth error: %s", errDescription), http.StatusBadRequest)
		return
	}

	state := r.URL.Query().Get("state")
	if state != "state" {
		debug.Error("Invalid state parameter: %s", state)
		http.Error(w, "Invalid state parameter", http.StatusBadRequest)
		return
	}

	code := r.URL.Query().Get("code")
	if code == "" {
		debug.Warn("GoogleCallbackHandler - Missing authorization code")
		http.Error(w, "No authorization code received", http.StatusBadRequest)
		return
	}

	debug.Debug("Exchanging authorization code for token")
	token, err := config.Exchange(context.Background(), code)
	if err != nil {
		debug.Error("GoogleCallbackHandler - Failed to exchange token: %v", err)
		http.Error(w, "Failed to exchange code for token", http.StatusInternalServerError)
		return
	}

	// Use the token to authenticate the user
	ctx := context.Background()
	ts := config.TokenSource(ctx, token)
	client := oauth2.NewClient(ctx, ts)

	userInfoService, err := v2.NewService(ctx, option.WithHTTPClient(client))
	if err != nil {
		debug.Error("GoogleCallbackHandler - Failed to get user info: %v", err)
		http.Error(w, "Failed to create userinfo service", http.StatusInternalServerError)
		return
	}

	userInfo, err := userInfoService.Userinfo.Get().Do()
	if err != nil {
		debug.Error("GoogleCallbackHandler - Failed to get user info: %v", err)
		http.Error(w, "Failed to get user info", http.StatusInternalServerError)
		return
	}

	// Create or get user account for Google OAuth
	user, err := CreateOrGetGoogleUser(userInfo.Email)
	if err != nil {
		debug.Error("GoogleCallbackHandler - Failed to get/create user %s: %v", userInfo.Email, err)
		http.Error(w, fmt.Sprintf("Failed to create/get user account: %v", err), http.StatusInternalServerError)
		return
	}

	// Generate session ID
	sessionID, err := generateSessionID()
	if err != nil {
		debug.Error("GoogleCallbackHandler - Failed to create session for %s: %v", userInfo.Email, err)
		http.Error(w, "Failed to generate session ID", http.StatusInternalServerError)
		return
	}

	// Get user permissions for session
	permissions, err := session.Manager.GetUserPermissions(user.Email)
	if err != nil {
		debug.Error("Failed to get permissions for user %s: %v", user.Email, err)
		// SECURITY: Fail the login if we cannot determine permissions
		http.Error(w, "Authentication failed - unable to determine permissions", http.StatusInternalServerError)
		return
	}

	// Get client IP address
	clientIP := session.GetClientIP(r)

	// Create Redis session
	err = session.Manager.CreateSession(sessionID, user.ID, user.Email, "google", permissions, clientIP)
	if err != nil {
		debug.Error("GoogleCallbackHandler - Failed to create session for %s: %v", user.Email, err)
		http.Error(w, "Failed to create session", http.StatusInternalServerError)
		return
	}

	// Set session cookie
	cookie := &http.Cookie{
		Name:     "session_token",
		Value:    sessionID,
		Path:     "/",
		MaxAge:   86400 * 7, // 7 days
		HttpOnly: true,
		Secure:   false, // Set to true in production with HTTPS
		SameSite: http.SameSiteLaxMode,
	}
	http.SetCookie(w, cookie)

	debug.Debug("Redis session created successfully for user: %s", user.Email)

	http.Redirect(w, r, "/", http.StatusFound)
}

// CreateOrGetGoogleUser creates or retrieves a user for Google OAuth login
func CreateOrGetGoogleUser(email string) (*users.User, error) {
	// Validate email format - basic check for @ and . characters
	if !strings.Contains(email, "@") || !strings.Contains(email, ".") {
		return nil, fmt.Errorf("invalid email format")
	}

	return users.CreateOrUpdateUser(email, "google")
}