<script>
	import { onMount } from 'svelte';

	export let userInfo = null;
	let loading = true;
	let error = null;

	let passwordForm = {
		currentPassword: '',
		newPassword: '',
		confirmPassword: ''
	};
	let passwordLoading = false;
	let passwordMessage = null;

	// Reactive statement that runs when userInfo prop changes
	$: {
		if (userInfo) {
			loading = false;
			error = null;
			console.log('Profile: Using userInfo prop:', userInfo.email);
		} else {
			loading = false;
			error = 'User information not available';
		}
	}

	function formatDate(dateString) {
		if (!dateString || dateString === null || dateString === undefined) {
			return 'Never';
		}
		try {
			const date = new Date(dateString);
			if (isNaN(date.getTime())) {
				return 'Invalid Date';
			}
			return date.toLocaleString();
		} catch (err) {
			console.error('Error formatting date:', err);
			return 'Invalid Date';
		}
	}

	function formatLoginMethod(method) {
		if (!method || method === null || method === undefined) {
			return 'Unknown';
		}
		return method === 'google' ? 'Google OAuth' : 'Local Account';
	}

	async function handlePasswordChange() {
		if (passwordLoading) {
			console.log('Profile: Password change already in progress');
			return;
		}

		passwordLoading = true;
		passwordMessage = null;

		// Validate form data
		if (!passwordForm.currentPassword || !passwordForm.newPassword || !passwordForm.confirmPassword) {
			passwordMessage = { type: 'error', text: 'All password fields are required' };
			passwordLoading = false;
			return;
		}

		if (passwordForm.newPassword !== passwordForm.confirmPassword) {
			passwordMessage = { type: 'error', text: 'New passwords do not match' };
			passwordLoading = false;
			return;
		}

		try {
			const formData = new FormData();
			formData.append('current_password', passwordForm.currentPassword);
			formData.append('new_password', passwordForm.newPassword);
			formData.append('confirm_password', passwordForm.confirmPassword);

			console.log('Profile: Submitting password change...');
			const response = await fetch('/change-password', {
				method: 'POST',
				body: formData
			});

			if (response.ok) {
				passwordMessage = { type: 'success', text: 'Password changed successfully!' };
				// Clear form
				passwordForm = {
					currentPassword: '',
					newPassword: '',
					confirmPassword: ''
				};
			} else {
				const errorText = await response.text();
				passwordMessage = { type: 'error', text: errorText || 'Failed to change password' };
			}
		} catch (err) {
			console.error('Profile: Error changing password:', err);
			passwordMessage = { type: 'error', text: 'Failed to change password' };
		} finally {
			passwordLoading = false;
		}
	}

	// TOTP MFA variables
	let totpEnabled = false;
	let showTotpSetup = false;
	let totpSetupData = null;
	let totpVerificationCode = '';
	let totpError = '';
	let totpSuccess = '';
	let showDisableTotp = false;
	let disableTotpPassword = '';

	async function checkTotpStatus() {
		try {
			const response = await fetch('/api/totp/status');
			if (response.ok) {
				const data = await response.json();
				totpEnabled = data.enabled;
			}
		} catch (err) {
			console.error('Failed to check TOTP status:', err);
		}
	}

	async function setupTotp() {
		try {
			const response = await fetch('/api/totp/setup', {
				method: 'POST'
			});

			if (response.ok) {
				const data = await response.json();
				totpSetupData = data;
				showTotpSetup = true;
				totpError = '';
			} else {
				const errorData = await response.text();
				totpError = errorData || 'Failed to setup TOTP';
			}
		} catch (err) {
			totpError = 'Network error occurred';
		}
	}

	async function verifyTotpSetup() {
		if (!totpVerificationCode.trim()) {
			totpError = 'Please enter the verification code';
			return;
		}

		try {
			const formData = new FormData();
			formData.append('code', totpVerificationCode.trim());

			const response = await fetch('/api/totp/verify-setup', {
				method: 'POST',
				body: formData
			});

			if (response.ok) {
				totpSuccess = 'TOTP enabled successfully!';
				totpEnabled = true;
				showTotpSetup = false;
				totpSetupData = null;
				totpVerificationCode = '';
				totpError = '';
			} else {
				const errorData = await response.text();
				totpError = errorData || 'Invalid verification code';
			}
		} catch (err) {
			totpError = 'Network error occurred';
		}
	}

	async function disableTotp() {
		if (!disableTotpPassword.trim()) {
			totpError = 'Please enter your current password';
			return;
		}

		try {
			const formData = new FormData();
			formData.append('current_password', disableTotpPassword);

			const response = await fetch('/api/totp/disable', {
				method: 'POST',
				body: formData
			});

			if (response.ok) {
				totpSuccess = 'TOTP disabled successfully!';
				totpEnabled = false;
				showDisableTotp = false;
				disableTotpPassword = '';
				totpError = '';
			} else {
				const errorData = await response.text();
				totpError = errorData || 'Failed to disable TOTP';
			}
		} catch (err) {
			totpError = 'Network error occurred';
		}
	}

	onMount(async () => {
		await fetchUserInfo();
		await checkTotpStatus();
	});

	let currentPassword = '';
  let newPassword = '';
  let confirmPassword = '';
  let passwordError = '';
  let passwordSuccess = '';
  let passwordLoadingNew = false;

  async function changePassword() {
    if (newPassword !== confirmPassword) {
      passwordError = 'New passwords do not match';
      return;
    }

    if (newPassword.length < 6) {
      passwordError = 'New password must be at least 6 characters long';
      return;
    }

    try {
      passwordLoadingNew = true;
      passwordError = '';
      passwordSuccess = '';

      const formData = new FormData();

      // Only require current password for local users
      if (userInfo.last_login_method === 'local') {
        if (!currentPassword) {
          passwordError = 'Current password is required';
          return;
        }
        formData.append('current_password', currentPassword);
      }

      formData.append('new_password', newPassword);

      const response = await fetch('/change-password', {
        method: 'POST',
        body: formData
      });

      if (response.ok) {
        const result = await response.json();
        passwordSuccess = result.message || 'Password changed successfully!';

        // Clear form
        currentPassword = '';
        newPassword = '';
        confirmPassword = '';
      } else {
        const errorText = await response.text();
        passwordError = errorText || 'Failed to change password';
      }
    } catch (err) {
      console.error('Password change error:', err);
      passwordError = 'Network error. Please try again.';
    } finally {
      passwordLoadingNew = false;
    }
  }

  async function sendPasswordResetEmail() {
    try {
      passwordLoadingNew = true;
      passwordError = '';
      passwordSuccess = '';

      const response = await fetch('/request-password-reset', {
        method: 'POST'
      });

      if (response.ok) {
        const result = await response.json();
        passwordSuccess = result.message || 'Password reset email sent successfully!';
      } else {
        const errorText = await response.text();
        passwordError = errorText || 'Failed to send password reset email';
      }
    } catch (err) {
      console.error('Password reset email error:', err);
      passwordError = 'Network error. Please try again.';
    } finally {
      passwordLoadingNew = false;
    }
  }
</script>

<div class="profile-container">
	<h1>User Profile</h1>

	{#if loading}
		<div class="loading">Loading user information...</div>
	{:else if error}
		<div class="error">
			<p>{error}</p>
			<button on:click={() => window.location.reload()}>Retry</button>
		</div>
	{:else if userInfo && typeof userInfo === 'object'}
		<div class="profile-card">
			<div class="profile-section">
				<h2>Account Information</h2>
				<div class="info-grid">
					<div class="info-item">
						<span class="label">Email:</span>
						<span class="value">{userInfo.email || 'N/A'}</span>
					</div>
					<div class="info-item">
						<span class="label">User ID:</span>
						<span class="value">{userInfo.id || 'N/A'}</span>
					</div>
					<div class="info-item">
						<span class="label">Account Status:</span>
						<span class="value status" class:enabled={userInfo.enabled} class:disabled={!userInfo.enabled}>
							{userInfo.enabled ? 'Enabled' : 'Disabled'}
						</span>
					</div>
					<div class="info-item">
						<span class="label">Account Created:</span>
						<span class="value">{formatDate(userInfo.created_at)}</span>
					</div>
				</div>
			</div>

			<div class="profile-section">
				<h2>Login Information</h2>
				<div class="info-grid">
					<div class="info-item">
						<span class="label">Last Login Method:</span>
						<span class="value login-method">{formatLoginMethod(userInfo.last_login_method)}</span>
					</div>
					<div class="info-item">
						<span class="label">Last Login:</span>
						<span class="value">{formatDate(userInfo.last_login_at)}</span>
					</div>
				</div>

				{#if userInfo.ip_history && Array.isArray(userInfo.ip_history) && userInfo.ip_history.length > 0}
					<div class="ip-history-section">
						<h3>Recent IP Addresses</h3>
						<div class="ip-history-list">
							{#each userInfo.ip_history as ipRecord, index}
								<div class="ip-record">
									<span class="ip-address">{ipRecord.IPAddress || 'Unknown IP'}</span>
									<span class="ip-date">{formatDate(new Date(ipRecord.Timestamp * 1000).toISOString())}</span>
								</div>
							{/each}
						</div>
					</div>
				{:else}
					<div class="ip-history-section">
						<h3>Recent IP Addresses</h3>
						<div class="no-ip-history">
							<p>No IP history available</p>
							{#if userInfo.ip_history}
								<p class="debug-info">Debug: IP history data type: {typeof userInfo.ip_history}, Length: {Array.isArray(userInfo.ip_history) ? userInfo.ip_history.length : 'Not an array'}</p>
								<pre class="debug-data">{JSON.stringify(userInfo.ip_history, null, 2)}</pre>
							{/if}
						</div>
					</div>
				{/if}
			</div>

			<div class="profile-section">
				<h2>Change Password</h2>
				<div class="card">
          <h3>Change Password</h3>

          {#if passwordError}
            <div class="error-message">{passwordError}</div>
          {/if}

          {#if passwordSuccess}
            <div class="success-message">{passwordSuccess}</div>
          {/if}

          {#if userInfo.last_login_method === 'google'}
            <div class="password-reset-info">
              <p>Since you signed in with Google, your local password was automatically generated. You can either:</p>
              <ul>
                <li>Send yourself a password reset email to set a new password</li>
                <li>Continue using Google Sign-In</li>
              </ul>
            </div>

            <button 
              type="button" 
              class="submit-btn reset-btn" 
              on:click={sendPasswordResetEmail}
              disabled={passwordLoadingNew}
            >
              {passwordLoadingNew ? 'Sending...' : 'Send Password Reset Email'}
            </button>

            <div class="divider">
              <span>or set password directly</span>
            </div>
          {/if}

          <form on:submit|preventDefault={changePassword}>
            {#if userInfo.last_login_method === 'local'}
              <div class="form-group">
                <label for="currentPassword">Current Password</label>
                <input
                  type="password"
                  id="currentPassword"
                  bind:value={currentPassword}
                  required
                  disabled={passwordLoadingNew}
                />
                <small>Don't know your current password? <button type="button" class="link-btn" on:click={sendPasswordResetEmail}>Send reset email</button></small>
              </div>
            {/if}

            <div class="form-group">
              <label for="newPassword">New Password</label>
              <input
                type="password"
                id="newPassword"
                bind:value={newPassword}
                required
                disabled={passwordLoadingNew}
                minlength="6"
              />
            </div>

            <div class="form-group">
              <label for="confirmPassword">Confirm New Password</label>
              <input
                type="password"
                id="confirmPassword"
                bind:value={confirmPassword}
                required
                disabled={passwordLoadingNew}
                minlength="6"
              />
            </div>

            <button type="submit" class="submit-btn" disabled={passwordLoadingNew}>
              {passwordLoadingNew ? 'Changing...' : 'Change Password'}
            </button>
          </form>
        </div>
			</div>

			<div class="profile-actions">
				<button class="secondary-button" on:click={() => window.history.back()}>
					← Back
				</button>
				<a href="/logout" class="danger-button">
					Logout
				</a>
			</div>
		</div>
	{:else}
		<div class="error">
			<p>No user information available</p>
			<button on:click={() => window.location.reload()}>Retry</button>
		</div>
	{/if}

	<!-- TOTP MFA Section -->
	<div class="profile-section">
		<h3>Two-Factor Authentication</h3>

		{#if totpError}
			<div class="error-message">{totpError}</div>
		{/if}

		{#if totpSuccess}
			<div class="success-message">{totpSuccess}</div>
		{/if}

		<div class="totp-status">
			<p>Status: <span class="status {totpEnabled ? 'enabled' : 'disabled'}">{totpEnabled ? 'Enabled' : 'Disabled'}</span></p>

			{#if !totpEnabled}
				<button class="btn btn-primary" on:click={setupTotp}>Enable Two-Factor Authentication</button>
			{:else}
				<button class="btn btn-danger" on:click={() => showDisableTotp = true}>Disable Two-Factor Authentication</button>
			{/if}
		</div>

		{#if showTotpSetup && totpSetupData}
			<div class="totp-setup-modal">
				<div class="modal-content">
					<h4>Setup Two-Factor Authentication</h4>
					<p>Scan this QR code with your authenticator app (Google Authenticator, Authy, etc.)</p>

					<div class="qr-code">
						<img src="data:image/png;base64,{btoa(String.fromCharCode(...new Uint8Array(totpSetupData.qr_code)))}" alt="QR Code" />
					</div>

					<p>Or enter this key manually: <code>{totpSetupData.manual_key}</code></p>

					<div class="form-group">
						<label for="totpCode">Enter verification code from your app:</label>
						<input
							type="text"
							id="totpCode"
							bind:value={totpVerificationCode}
							placeholder="000000"
							maxlength="6"
							class="form-input"
						/>
					</div>

					<div class="modal-buttons">
						<button class="btn btn-primary" on:click={verifyTotpSetup}>Verify & Enable</button>
						<button class="btn btn-secondary" on:click={() => {showTotpSetup = false; totpSetupData = null; totpVerificationCode = ''; totpError = '';}}>Cancel</button>
					</div>
				</div>
			</div>
		{/if}

		{#if showDisableTotp}
			<div class="totp-setup-modal">
				<div class="modal-content">
					<h4>Disable Two-Factor Authentication</h4>
					{#if userInfo.last_login_method === 'google'}
						<p>You are signed in using your Google account. To disable two-factor authentication, you need to re-authenticate with Google first.</p>
						<div class="info-message">
							<p><strong>Note:</strong> Google OAuth users cannot provide a password for verification. Please contact an administrator if you need to disable two-factor authentication.</p>
						</div>
						<div class="modal-buttons">
							<button class="btn btn-secondary" on:click={() => {showDisableTotp = false; disableTotpPassword = ''; totpError = '';}}>Cancel</button>
						</div>
					{:else}
						<p>Enter your password to disable two-factor authentication:</p>

						<div class="form-group">
							<label for="disablePassword">Current Password:</label>
							<input
								type="password"
								id="disablePassword"
								bind:value={disableTotpPassword}
								placeholder="Enter your current password"
								class="form-input"
							/>
						</div>

						<div class="modal-buttons">
							<button class="btn btn-danger" on:click={disableTotp}>Disable TOTP</button>
							<button class="btn btn-secondary" on:click={() => {showDisableTotp = false; disableTotpPassword = ''; totpError = '';}}>Cancel</button>
						</div>
					{/if}
				</div>
			</div>
		{/if}
	</div>
</div>

<style>
	.profile-container {
		max-width: 800px;
		margin: 2rem auto;
		padding: 0 1rem;
	}

	h1 {
		color: #333;
		margin-bottom: 2rem;
		text-align: center;
	}

	.loading {
		text-align: center;
		padding: 2rem;
		color: #666;
	}

	.error {
		text-align: center;
		padding: 2rem;
		color: #dc3545;
	}

	.error button {
		margin-top: 1rem;
		padding: 0.5rem 1rem;
		background-color: #007bff;
		color: white;
		border: none;
		border-radius: 4px;
		cursor: pointer;
	}

	.profile-card {
		background: white;
		border-radius: 8px;
		box-shadow: 0 2px 10px rgba(0,0,0,0.1);
		overflow: hidden;
	}

	.profile-section {
		padding: 2rem;
		border-bottom: 1px solid #f0f0f0;
	}

	.profile-section:last-of-type {
		border-bottom: none;
	}

	.profile-section h2 {
		margin: 0 0 1.5rem 0;
		color: #333;
		font-size: 1.4rem;
	}

	.info-grid {
		display: grid;
		gap: 1rem;
	}

	.info-item {
		display: grid;
		grid-template-columns: 200px 1fr;
		gap: 1rem;
		align-items: center;
	}

	.info-item .label {
		font-weight: 600;
		color: #555;
		margin: 0;
	}

	.info-item .value {
		color: #333;
		background-color: #f8f9fa;
		padding: 0.5rem;
		border-radius: 4px;
	}

	.login-method {
		background-color: #e7f3ff !important;
		color: #0066cc !important;
		font-weight: 500;
	}

	.status.enabled {
		background-color: #d4edda !important;
		color: #155724 !important;
		font-weight: 500;
	}

	.status.disabled {
		background-color: #f8d7da !important;
		color: #721c24 !important;
		font-weight: 500;
	}

	.ip-history-section {
		margin-top: 2rem;
		padding-top: 1.5rem;
		border-top: 1px solid #e9ecef;
	}

	.ip-history-section h3 {
		margin: 0 0 1rem 0;
		color: #495057;
		font-size: 1.2rem;
	}

	.ip-history-list {
		display: flex;
		flex-direction: column;
		gap: 0.75rem;
	}

	.ip-record {
		display: grid;
		grid-template-columns: 1fr auto;
		gap: 1rem;
		align-items: center;
		padding: 0.75rem;
		background-color: #f8f9fa;
		border-radius: 6px;
		border-left: 4px solid #6c757d;
	}

	.ip-address {
		font-family: 'Courier New', monospace;
		font-weight: 600;
		color: #495057;
		background-color: #e9ecef;
		padding: 0.25rem 0.5rem;
		border-radius: 4px;
		font-size: 0.9rem;
	}

	.ip-date {
		color: #6c757d;
		font-size: 0.85rem;
		text-align: right;
	}

	.no-ip-history {
		padding: 1rem;
		background-color: #f8f9fa;
		border-radius: 6px;
		text-align: center;
		color: #6c757d;
	}

	.debug-info {
		font-size: 0.8rem;
		color: #dc3545;
		margin-top: 0.5rem;
	}

	.debug-data {
		background-color: #f1f3f4;
		padding: 0.5rem;
		border-radius: 4px;
		font-size: 0.7rem;
		color: #333;
		text-align: left;
		margin-top: 0.5rem;
		overflow-x: auto;
	}

	.profile-actions {
		padding: 2rem;
		display: flex;
		gap: 1rem;
		justify-content: space-between;
		background-color: #f8f9fa;
	}

	.secondary-button {
		padding: 0.75rem 1.5rem;
		background-color: #6c757d;
		color: white;
		border: none;
		border-radius: 4px;
		cursor: pointer;
		text-decoration: none;
		display: inline-block;
	}

	.secondary-button:hover {
		background-color: #5a6268;
	}

	.danger-button {
		padding: 0.75rem 1.5rem;
		background-color: #dc3545;
		color: white;
		border: none;
		border-radius: 4px;
		cursor: pointer;
		text-decoration: none;
		display: inline-block;
	}

	.danger-button:hover {
		background-color: #c82333;
	}

	.password-form {
		max-width: 400px;
	}

	.form-group {
		margin-bottom: 1rem;
	}

	.form-group label {
		display: block;
		margin-bottom: 0.5rem;
		font-weight: 600;
		color: #555;
	}

	.form-group input {
		width: 100%;
		padding: 0.75rem;
		border: 1px solid #ddd;
		border-radius: 4px;
		font-size: 16px;
		box-sizing: border-box;
	}

	.form-group input:focus {
		outline: none;
		border-color: #007bff;
		box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
	}

	.form-group input:disabled {
		background-color: #f8f9fa;
		cursor: not-allowed;
	}

	.primary-button {
		padding: 0.75rem 1.5rem;
		background-color: #007bff;
		color: white;
		border: none;
		border-radius: 4px;
		cursor: pointer;
		font-size: 16px;
	}

	.primary-button:hover:not(:disabled) {
		background-color: #0056b3;
	}

	.primary-button:disabled {
		background-color: #6c757d;
		cursor: not-allowed;
	}

	.message {
		padding: 0.75rem;
		border-radius: 4px;
		margin-bottom: 1rem;
	}

	.message.success {
		background-color: #d4edda;
		color: #155724;
		border: 1px solid #c3e6cb;
	}

	.message.error {
		background-color: #f8d7da;
		color: #721c24;
		border: 1px solid #f5c6cb;
	}

	.google-auth-notice {
		max-width: 500px;
	}

	.info-message {
		background-color: #e7f3ff;
		color: #0066cc;
		padding: 1.5rem;
		border-radius: 8px;
		border: 1px solid #b3d9ff;
	}

	.info-message p {
		margin: 0 0 1rem 0;
	}

	.info-message p:last-child {
		margin-bottom: 0;
	}

	.info-message a {
		color: #0052cc;
		text-decoration: underline;
	}

	.info-message a:hover {
		color: #003d99;
	}

	@media (max-width: 600px) {
		.info-item {
			grid-template-columns: 1fr;
			gap: 0.5rem;
		}

		.profile-actions {
			flex-direction: column;
		}

		.ip-record {
			grid-template-columns: 1fr;
			gap: 0.5rem;
		}

		.ip-date {
			text-align: left;
			font-size: 0.8rem;
		}
	}

	.btn {
		padding: 10px 20px;
		border: none;
		border-radius: 5px;
		cursor: pointer;
		font-size: 16px;
		transition: background-color 0.3s ease;
		text-decoration: none;
		display: inline-block;
	}

	.btn-primary {
		background-color: #007bff;
		color: white;
	}

	.btn-primary:hover {
		background-color: #0056b3;
	}

	.btn-secondary {
		background-color: #6c757d;
		color: white;
	}

	.btn-secondary:hover {
		background-color: #5a6268;
	}

	.btn-danger {
		background-color: #dc3545;
		color: white;
	}

	.btn-danger:hover {
		background-color: #c82333;
	}

	.totp-status {
		margin: 20px 0;
	}

	.status {
		font-weight: bold;
		padding: 4px 8px;
		border-radius: 4px;
	}

	.status.enabled {
		background-color: #d4edda;
		color: #155724;
	}

	.status.disabled {
		background-color: #f8d7da;
		color: #721c24;
	}

	.totp-setup-modal {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: rgba(0, 0, 0, 0.5);
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 1000;
	}

	.modal-content {
		background: white;
		padding: 30px;
		border-radius: 8px;
		max-width: 500px;
		width: 90%;
		max-height: 90vh;
		overflow-y: auto;
	}

	.qr-code {
		text-align: center;
		margin: 20px 0;
	}

	.qr-code img {
		max-width: 200px;
		height: auto;
	}

	.modal-buttons {
		display: flex;
		gap: 10px;
		justify-content: flex-end;
		margin-top: 20px;
	}

	.form-group {
		margin: 15px 0;
	}

	.form-group label {
		display: block;
		margin-bottom: 5px;
		font-weight: bold;
	}

	.form-input {
		width: 100%;
		padding: 8px 12px;
		border: 1px solid #ddd;
		border-radius: 4px;
		font-size: 14px;
	}

	code {
		background-color: #f8f9fa;
		padding: 2px 4px;
		border-radius: 3px;
		font-family: 'Courier New', monospace;
		word-break: break-all;
	}
	.totp-help {
    font-size: 12px;
    color: #666;
    margin-top: 5px;
  }

  .password-reset-info {
    background-color: #f0f7ff;
    border: 1px solid #c7e2ff;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 20px;
  }

  .password-reset-info p {
    margin: 0 0 10px 0;
    color: #0066cc;
  }

  .password-reset-info ul {
    margin: 0;
    padding-left: 20px;
    color: #0066cc;
  }

  .reset-btn {
    background-color: #28a745;
    margin-bottom: 20px;
  }

  .reset-btn:hover:not(:disabled) {
    background-color: #218838;
  }

  .divider {
    text-align: center;
    margin: 20px 0;
    position: relative;
  }

  .divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #ddd;
  }

  .divider span {
    background: white;
    padding: 0 15px;
    color: #666;
    font-size: 14px;
  }

  .link-btn {
    background: none;
    border: none;
    color: #007bff;
    cursor: pointer;
    text-decoration: underline;
    padding: 0;
    font-size: inherit;
  }

  .link-btn:hover {
    color: #0056b3;
  }

  small {
    display: block;
    margin-top: 5px;
    color: #666;
    font-size: 12px;
  }
</style>