// Added GetUserByID function to retrieve user by ID.
package auth

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"strings"

	"web-dashboard/backend/database"
	"web-dashboard/backend/debug"
	"web-dashboard/backend/session"
	"web-dashboard/backend/users"

	"github.com/gorilla/sessions"
	"golang.org/x/oauth2"
)

var (
	store       *sessions.CookieStore
	oauthConfig *oauth2.Config
)

// GetBaseURL returns the base URL for the application from system settings
func GetBaseURL(r *http.Request) string {
	// Try to get from system settings first
	siteURL := database.GetSettingString("site_url", "")
	if siteURL != "" {
		return strings.TrimSuffix(siteURL, "/")
	}

	// Fallback to request host
	scheme := "https"
	if r.TLS == nil {
		scheme = "http"
	}

	host := r.Host
	if host == "" {
		host = r.Header.Get("Host")
	}

	return fmt.Sprintf("%s://%s", scheme, host)
}

func Init(s *sessions.CookieStore) {
	store = s

	// Initialize Google OAuth
	InitGoogleOAuth()
}

func AuthHandler(w http.ResponseWriter, r *http.Request) {
	// For GET requests, check if it's a Google OAuth request
	if r.Method == "GET" {
		// Check if this is a Google OAuth request
		if r.URL.Query().Get("provider") == "google" {
			HandleGoogleOAuth(w, r)
			return
		}

		// For auth page requests, always serve the auth page without checking authentication
		// This prevents redirect loops
		w.Header().Set("Content-Type", "text/html; charset=utf-8")

		// Check if we're in testing mode
		if os.Getenv("TESTING") == "true" {
			// In test mode, just return a simple HTML response
			w.Write([]byte(`<!DOCTYPE html><html><head><title>Auth</title></head><body><h1>Auth Page</h1></body></html>`))
			return
		}

		http.ServeFile(w, r, "../frontend/public/index.html")
		return
	}

	// For POST requests, handle Google OAuth
	if r.Method == "POST" && r.FormValue("provider") == "google" {
		HandleGoogleOAuth(w, r)
		return
	}

	// For all other HTTP methods, return 405 Method Not Allowed
	http.Error(w, "Method Not Allowed", http.StatusMethodNotAllowed)
}

func CallbackHandler(w http.ResponseWriter, r *http.Request) {
	GoogleCallbackHandler(w, r)
}

// AuthMiddleware checks if user is authenticated
func AuthMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Check if this is an API request by path or headers
		isAPIRequest := strings.HasPrefix(r.URL.Path, "/api/") ||
			strings.Contains(r.Header.Get("Accept"), "application/json") ||
			strings.Contains(r.Header.Get("Content-Type"), "application/json")

		// Get session token from cookie
		cookie, err := r.Cookie("session_token")
		if err != nil {
			debug.Debug("No session cookie found for %s: %v", r.URL.Path, err)
			if isAPIRequest {
				w.Header().Set("Content-Type", "application/json")
				w.WriteHeader(http.StatusUnauthorized)
				json.NewEncoder(w).Encode(map[string]string{"error": "unauthorized", "message": "Not authenticated"})
				return
			}
			// Clear any existing session cookies that might be corrupted
			clearCookie := &http.Cookie{
				Name:     "session_token",
				Value:    "",
				Path:     "/",
				MaxAge:   -1,
				HttpOnly: true,
				Secure:   false,
				SameSite: http.SameSiteLaxMode,
			}
			http.SetCookie(w, clearCookie)
			http.Redirect(w, r, "/auth", http.StatusFound)
			return
		}

		// Get session from Redis
		sessionData, err := session.Manager.GetSession(cookie.Value)
		if err != nil {
			debug.Debug("Failed to get Redis session for %s: %v", r.URL.Path, err)
			// Clear the invalid session cookie
			clearCookie := &http.Cookie{
				Name:     "session_token",
				Value:    "",
				Path:     "/",
				MaxAge:   -1,
				HttpOnly: true,
				Secure:   false,
				SameSite: http.SameSiteLaxMode,
			}
			http.SetCookie(w, clearCookie)

			if isAPIRequest {
				w.Header().Set("Content-Type", "application/json")
				w.WriteHeader(http.StatusUnauthorized)
				json.NewEncoder(w).Encode(map[string]string{"error": "unauthorized", "message": "Session expired or invalid"})
				return
			}
			http.Redirect(w, r, "/auth", http.StatusFound)
			return
		}

		// Check if this is a System Settings request
		isSystemSettingsRequest := strings.HasPrefix(r.URL.Path, "/api/system-settings")

		if isSystemSettingsRequest {
			// For System Settings, require specific permissions
			hasSystemSettingsAccess := false
			if sessionData.Permissions != nil {
				for _, perm := range sessionData.Permissions {
					// Allow super admin (*) or specific system settings permissions
					if perm == "*" || strings.Contains(perm, "System Settings") || strings.Contains(perm, "User Management") {
						hasSystemSettingsAccess = true
						break
					}
				}
			}

			if !hasSystemSettingsAccess {
				debug.Warn("User %s lacks System Settings access for %s", sessionData.Email, r.URL.Path)
				if isAPIRequest {
					w.Header().Set("Content-Type", "application/json")
					w.WriteHeader(http.StatusForbidden)
					json.NewEncoder(w).Encode(map[string]string{"error": "forbidden", "message": "System Settings access required"})
					return
				}
				http.Redirect(w, r, "/auth", http.StatusFound)
				return
			}
		} else {
			// For non-System Settings requests, check basic user access
			hasBasicAccess := false
			if sessionData.Permissions != nil {
				for _, perm := range sessionData.Permissions {
					// Allow super admin (*) or any read permission
					if perm == "*" || perm == "Read Access" || perm == "system:read" {
						hasBasicAccess = true
						break
					}
				}
			}

			if !hasBasicAccess {
				debug.Warn("User %s lacks basic user group access", sessionData.Email)
				if isAPIRequest {
					w.Header().Set("Content-Type", "application/json")
					w.WriteHeader(http.StatusForbidden)
					json.NewEncoder(w).Encode(map[string]string{"error": "forbidden", "message": "Basic user group access required"})
					return
				}
				http.Redirect(w, r, "/auth", http.StatusFound)
				return
			}
		}

		debug.Debug("AuthMiddleware - User authenticated: %s", sessionData.Email)

		// Store session data in request context for later use
		ctx := context.WithValue(r.Context(), "session", sessionData)
		r = r.WithContext(ctx)

		next.ServeHTTP(w, r)
	})
}

// UserInfoHandler returns the current user's information
func UserInfoHandler(w http.ResponseWriter, r *http.Request) {
	debug.Debug("UserInfoHandler called from %s", r.RemoteAddr)

	userEmail, err := GetCurrentUserEmail(r)
	if err != nil {
		debug.Debug("UserInfoHandler - GetCurrentUserEmail failed: %v", err)
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusUnauthorized)
		json.NewEncoder(w).Encode(map[string]string{"error": "unauthorized", "message": "Not authenticated"})
		return
	}

	debug.Debug("UserInfoHandler - Retrieved user email: %s", userEmail)

	// Get user info from database
	user, err := users.GetUserByEmail(userEmail)
	if err != nil {
		debug.Error("UserInfoHandler - GetUserByEmail failed for %s: %v", userEmail, err)
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusInternalServerError)
		json.NewEncoder(w).Encode(map[string]string{"error": "internal_error", "message": "Failed to get user info"})
		return
	}

	if user == nil {
		debug.Error("UserInfoHandler - GetUserByEmail returned nil user for %s", userEmail)
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusInternalServerError)
		json.NewEncoder(w).Encode(map[string]string{"error": "internal_error", "message": "User data not found"})
		return
	}

	debug.Debug("UserInfoHandler - Retrieved user data: ID=%d, Email=%s, Enabled=%t",
		user.ID, user.Email, user.Enabled)

	// Create response with user information
	userInfo := map[string]interface{}{
		"id":                user.ID,
		"email":             user.Email,
		"enabled":           user.Enabled,
		"last_login_method": user.LastLoginMethod,
		"last_login_at":     user.LastLoginAt,
		"created_at":        user.CreatedAt,
		"updated_at":        user.UpdatedAt,
	}

	debug.Debug("UserInfoHandler returning data for %s: %+v", userEmail, userInfo)

	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(userInfo); err != nil {
		debug.Error("UserInfoHandler - Failed to encode JSON response: %v", err)
		return
	}

	debug.Debug("UserInfoHandler completed successfully for %s", userEmail)
}

// GetCurrentUserEmail returns the email of the current authenticated user
func GetCurrentUserEmail(r *http.Request) (string, error) {
	debug.Debug("GetCurrentUserEmail called from %s", r.RemoteAddr)

	// Get session token from cookie
	cookie, err := r.Cookie("session_token")
	if err != nil {
		debug.Debug("No session cookie found: %v", err)
		return "", fmt.Errorf("user not authenticated")
	}

	// Get session from Redis
	sessionData, err := session.Manager.GetSession(cookie.Value)
	if err != nil {
		debug.Debug("Failed to get Redis session: %v", err)
		return "", fmt.Errorf("user not authenticated")
	}

	debug.Debug("GetCurrentUserEmail returning user: '%s'", sessionData.Email)
	return sessionData.Email, nil
}

// LogoutHandler handles user logout
func LogoutHandler(w http.ResponseWriter, r *http.Request) {
	// Get session token from cookie
	cookie, err := r.Cookie("session_token")
	if err != nil {
		// No session cookie, just redirect to auth
		http.Redirect(w, r, "/auth", http.StatusFound)
		return
	}

	// Delete session from Redis
	err = session.Manager.DeleteSession(cookie.Value)
	if err != nil {
		fmt.Printf("WARNING: Failed to delete Redis session: %v\n", err)

	}

	// Clear session cookie
	cookie = &http.Cookie{
		Name:     "session_token",
		Value:    "",
		Path:     "/",
		MaxAge:   -1,
		HttpOnly: true,
		Secure:   false,
		SameSite: http.SameSiteLaxMode,
	}
	http.SetCookie(w, cookie)

	// Redirect to auth page
	http.Redirect(w, r, "/auth", http.StatusFound)
}
