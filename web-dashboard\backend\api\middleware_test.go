
package api

import (
	"context"
	"net/http"
	"net/http/httptest"
	"testing"

	"web-dashboard/backend/session"

	"github.com/stretchr/testify/assert"
)

func TestCheckSessionPermission(t *testing.T) {
	tests := []struct {
		name                string
		sessionData         *session.SessionData
		requiredPermissions []string
		expected            bool
	}{
		{
			name:                "nil session data",
			sessionData:         nil,
			requiredPermissions: []string{"test"},
			expected:            false,
		},
		{
			name: "nil permissions",
			sessionData: &session.SessionData{
				Permissions: nil,
			},
			requiredPermissions: []string{"test"},
			expected:            false,
		},
		{
			name: "wildcard permission",
			sessionData: &session.SessionData{
				Permissions: []string{"*"},
			},
			requiredPermissions: []string{"test"},
			expected:            true,
		},
		{
			name: "specific permission match",
			sessionData: &session.SessionData{
				Permissions: []string{"System Settings", "User Management"},
			},
			requiredPermissions: []string{"System Settings"},
			expected:            true,
		},
		{
			name: "no permission match",
			sessionData: &session.SessionData{
				Permissions: []string{"Other Permission"},
			},
			requiredPermissions: []string{"System Settings"},
			expected:            false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := checkSessionPermission(tt.sessionData, tt.requiredPermissions...)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestRequirePermissions(t *testing.T) {
	middleware := requirePermissions("System Settings")

	// Test with valid permissions
	t.Run("valid permissions", func(t *testing.T) {
		sessionData := &session.SessionData{
			Permissions: []string{"System Settings"},
		}

		handler := middleware(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusOK)
		})

		req := httptest.NewRequest("GET", "/test", nil)
		ctx := context.WithValue(req.Context(), "session", sessionData)
		req = req.WithContext(ctx)
		
		rr := httptest.NewRecorder()
		handler.ServeHTTP(rr, req)

		assert.Equal(t, http.StatusOK, rr.Code)
	})

	// Test with invalid permissions
	t.Run("invalid permissions", func(t *testing.T) {
		sessionData := &session.SessionData{
			Permissions: []string{"Other Permission"},
		}

		handler := middleware(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusOK)
		})

		req := httptest.NewRequest("GET", "/test", nil)
		ctx := context.WithValue(req.Context(), "session", sessionData)
		req = req.WithContext(ctx)
		
		rr := httptest.NewRecorder()
		handler.ServeHTTP(rr, req)

		assert.Equal(t, http.StatusForbidden, rr.Code)
	})
}
