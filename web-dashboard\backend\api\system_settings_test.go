
package api

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"web-dashboard/backend/testutils"

	"github.com/gorilla/mux"
)

func TestSystemSettingsRoutes(t *testing.T) {
	testutils.SetupTestEnvironment()

	router := mux.NewRouter()
	AttachSystemSettingsRoutes(router)

	t.Run("GET /system-settings/users without auth returns 401", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/system-settings/users", nil)
		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		if rr.Code != http.StatusUnauthorized && rr.Code != http.StatusNotFound {
			t.<PERSON>("Expected 401 or 404, got %d", rr.Code)
		}
	})
}
