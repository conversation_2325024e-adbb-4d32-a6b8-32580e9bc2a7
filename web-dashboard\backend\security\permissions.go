package security

import (
	"database/sql"
	"fmt"

	"web-dashboard/backend/database"
	"web-dashboard/backend/debug"
)

type Permission struct {
	ID          int    `json:"id"`
	Name        string `json:"name"`
	Description string `json:"description"`
	Resource    string `json:"resource"`
	Action      string `json:"action"`
	CreatedAt   string `json:"created_at"`
	UpdatedAt   string `json:"updated_at"`
}

func GetAllPermissions() ([]Permission, error) {
	query := `SELECT id, name, description, resource, action, created_at, updated_at FROM permissions ORDER BY name`
	rows, err := database.DB.Query(query)
	if err != nil {
		return nil, fmt.Errorf("failed to query permissions: %v", err)
	}
	defer rows.Close()

	var permissions []Permission
	for rows.Next() {
		var permission Permission
		if err := rows.Scan(&permission.ID, &permission.Name, &permission.Description, &permission.Resource, &permission.Action, &permission.CreatedAt, &permission.UpdatedAt); err != nil {
			return nil, fmt.Errorf("failed to scan permission: %v", err)
		}
		permissions = append(permissions, permission)
	}
	return permissions, nil
}

func CreatePermission(name, description, resource, action string) (*Permission, error) {
	query := `INSERT INTO permissions (name, description, resource, action) VALUES ($1, $2, $3, $4) RETURNING id, name, description, resource, action, created_at, updated_at`
	var permission Permission
	err := database.DB.QueryRow(query, name, description, resource, action).Scan(
		&permission.ID, &permission.Name, &permission.Description, &permission.Resource, &permission.Action, &permission.CreatedAt, &permission.UpdatedAt)
	if err != nil {
		return nil, fmt.Errorf("failed to create permission: %v", err)
	}
	return &permission, nil
}

func GetPermission(id int) (*Permission, error) {
	query := `SELECT id, name, description, resource, action, created_at, updated_at FROM permissions WHERE id = $1`
	var permission Permission
	err := database.DB.QueryRow(query, id).Scan(
		&permission.ID, &permission.Name, &permission.Description, &permission.Resource, &permission.Action, &permission.CreatedAt, &permission.UpdatedAt)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("permission not found")
		}
		return nil, fmt.Errorf("failed to get permission: %v", err)
	}
	return &permission, nil
}

func DeletePermission(id int) error {
	query := `DELETE FROM permissions WHERE id = $1`
	result, err := database.DB.Exec(query, id)
	if err != nil {
		return fmt.Errorf("failed to delete permission: %v", err)
	}
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %v", err)
	}
	if rowsAffected == 0 {
		return fmt.Errorf("permission not found")
	}
	return nil
}

func GetUserPermissions(userID int) ([]Permission, error) {
	query := `
		SELECT DISTINCT p.id, p.name, p.description, p.resource, p.action, p.created_at, p.updated_at
		FROM permissions p
		JOIN security_group_permissions sgp ON p.id = sgp.permission_id
		JOIN user_security_groups usg ON sgp.security_group_id = usg.security_group_id
		WHERE usg.user_id = $1
	`
	rows, err := database.DB.Query(query, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to query user permissions: %v", err)
	}
	defer rows.Close()

	var permissions []Permission
	for rows.Next() {
		var permission Permission
		if err := rows.Scan(&permission.ID, &permission.Name, &permission.Description, &permission.Resource, &permission.Action, &permission.CreatedAt, &permission.UpdatedAt); err != nil {
			return nil, fmt.Errorf("failed to scan permission: %v", err)
		}
		permissions = append(permissions, permission)
	}
	return permissions, nil
}

func GetUserSecurityGroups(userID int) ([]SecurityGroup, error) {
	query := `
		SELECT sg.id, sg.name, sg.description, sg.created_at, sg.updated_at
		FROM security_groups sg
		JOIN user_security_groups usg ON sg.id = usg.security_group_id
		WHERE usg.user_id = $1
	`
	rows, err := database.DB.Query(query, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to query user security groups: %v", err)
	}
	defer rows.Close()

	var groups []SecurityGroup
	for rows.Next() {
		var group SecurityGroup
		if err := rows.Scan(&group.ID, &group.Name, &group.Description, &group.CreatedAt, &group.UpdatedAt); err != nil {
			return nil, fmt.Errorf("failed to scan security group: %v", err)
		}
		groups = append(groups, group)
	}
	return groups, nil
}

func UserHasPermission(userID int, resource, action string) (bool, error) {
	query := `
		SELECT COUNT(*)
		FROM permissions p
		JOIN security_group_permissions sgp ON p.id = sgp.permission_id
		JOIN user_security_groups usg ON sgp.security_group_id = usg.security_group_id
		WHERE usg.user_id = $1 AND p.resource = $2 AND p.action = $3
	`
	var count int
	err := database.DB.QueryRow(query, userID, resource, action).Scan(&count)
	if err != nil {
		return false, fmt.Errorf("failed to check user permission: %v", err)
	}
	return count > 0, nil
}

func EnsureUserInDefaultGroup(userID int, email string) error {
	debug.Debug("Checking security groups for user ID %d (%s)", userID, email)

	// Check if user is already in any security group
	var count int
	err := database.DB.QueryRow("SELECT COUNT(*) FROM user_security_groups WHERE user_id = $1", userID).Scan(&count)
	if err != nil {
		debug.Error("Failed to check user security groups for user %s: %v", email, err)
		return fmt.Errorf("failed to check user security groups: %v", err)
	}

	// If user is not in any security group, add them to the default "User" security group
	if count == 0 {
		debug.Info("User %s (ID: %d) has no security groups, adding to default User group", email, userID)
		// Get the "User" security group ID
		var groupID int
		err = database.DB.QueryRow("SELECT id FROM security_groups WHERE name = 'User'").Scan(&groupID)
		if err != nil {
			return fmt.Errorf("failed to get User security group: %v", err)
		}

		// Add user to the security group
		err = AddUserToSecurityGroup(userID, groupID)
		if err != nil {
			debug.Error("Failed to add user %s to User security group: %v", email, err)
			return fmt.Errorf("failed to add user to User security group: %v", err)
		}
		debug.Info("Successfully added user %s to User security group", email)
	} else {
		debug.Debug("User %s already belongs to %d security group(s)", email, count)
	}

	return nil
}