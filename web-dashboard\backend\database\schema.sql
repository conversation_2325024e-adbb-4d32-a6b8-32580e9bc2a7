-- Users table
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    enabled BOOLEAN DEFAULT TRUE,
    last_login_method VARCHAR(20) DEFAULT 'local',
    last_login_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Security Groups table
CREATE TABLE IF NOT EXISTS security_groups (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Permissions table
CREATE TABLE IF NOT EXISTS permissions (
    id SERIAL PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(255) UNIQUE NOT NULL,
    description TEXT,
    resource VARCHAR(255) NOT NULL,
    action VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(resource, action)
);

-- User-Security Group relationships (many-to-many)
CREATE TABLE IF NOT EXISTS user_security_groups (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    security_group_id INTEGER NOT NULL,
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (security_group_id) REFERENCES security_groups(id) ON DELETE CASCADE,
    UNIQUE(user_id, security_group_id)
);

-- Security Group-Permission relationships (many-to-many)
CREATE TABLE IF NOT EXISTS security_group_permissions (
    id SERIAL PRIMARY KEY,
    security_group_id INTEGER NOT NULL,
    permission_id INTEGER NOT NULL,
    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (security_group_id) REFERENCES security_groups(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE,
    UNIQUE(security_group_id, permission_id)
);

-- System settings table for dynamic configuration
CREATE TABLE IF NOT EXISTS system_settings (
    id SERIAL PRIMARY KEY,
    setting_key VARCHAR(255) UNIQUE NOT NULL,
    setting_value TEXT,
    data_type VARCHAR(50) NOT NULL DEFAULT 'string', -- string, integer, boolean, float, json
    description TEXT,
    category VARCHAR(100) DEFAULT 'general',
    is_encrypted BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indices for better performance
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_user_security_groups_user_id ON user_security_groups(user_id);
CREATE INDEX IF NOT EXISTS idx_user_security_groups_security_group_id ON user_security_groups(security_group_id);
CREATE INDEX IF NOT EXISTS idx_security_group_permissions_security_group_id ON security_group_permissions(security_group_id);
CREATE INDEX IF NOT EXISTS idx_security_group_permissions_permission_id ON security_group_permissions(permission_id);
CREATE INDEX IF NOT EXISTS idx_permissions_resource_action ON permissions(resource, action);
CREATE INDEX IF NOT EXISTS idx_system_settings_key ON system_settings(setting_key);
CREATE INDEX IF NOT EXISTS idx_system_settings_category ON system_settings(category);

-- User TOTP secrets table
CREATE TABLE IF NOT EXISTS user_totp_secrets (
    user_id INTEGER PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
    secret TEXT NOT NULL,
    enabled BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Password reset tokens table
CREATE TABLE IF NOT EXISTS password_reset_tokens (
    email TEXT PRIMARY KEY,
    token TEXT NOT NULL UNIQUE,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert default security groups
INSERT INTO security_groups (name, description) VALUES 
('Super Admin', 'Super administrators with full system access'),
('User', 'Default user security group with basic permissions')
ON CONFLICT (name) DO NOTHING;

-- Insert basic permissions
INSERT INTO permissions (name, description, resource, action) VALUES 
('Read Access', 'Basic read access to the system', 'system', 'read'),
('Profile Access', 'Access to user profile information', 'profile', 'read'),
('View Data', 'View application data', 'data', 'read')
ON CONFLICT (name) DO NOTHING;

-- Assign basic permissions to User security group
INSERT INTO security_group_permissions (security_group_id, permission_id)
SELECT g.id, p.id
FROM security_groups g, permissions p
WHERE g.name = 'User' 
AND p.resource IN ('system', 'profile', 'data')
AND p.action = 'read'
ON CONFLICT (security_group_id, permission_id) DO NOTHING;

-- Insert System Settings permissions
INSERT INTO permissions (name, description, resource, action) VALUES 
('System Settings - View', 'View system settings interface', 'system_settings', 'view'),
('System Settings - Manage Email', 'Manage email configuration and send test emails', 'system_settings', 'manage_email'),
('System Settings - Full Access', 'Full access to all system settings', 'system_settings', 'full_access'),
('User Management - View', 'View user management interface', 'user_management', 'view'),
('User Management - Activate', 'Activate and deactivate user accounts', 'user_management', 'activate'),
('User Management - Enable', 'Enable and disable user accounts', 'user_management', 'enable'),
('User Management - Delete', 'Delete user accounts', 'user_management', 'delete'),
('User Management - Full Access', 'Full access to user management features', 'user_management', 'full_access'),
('Group Management - View', 'View groups and group memberships', 'group_management', 'view'),
('Group Management - Create', 'Create new groups', 'group_management', 'create'),
('Group Management - Edit', 'Edit group details and memberships', 'group_management', 'edit'),
('Group Management - Delete', 'Delete groups', 'group_management', 'delete'),
('Group Management - Full Access', 'Full access to group management features', 'group_management', 'full_access'),
('Permission Management - View', 'View permissions and permission assignments', 'permission_management', 'view'),
('Permission Management - Create', 'Create new permissions', 'permission_management', 'create'),
('Permission Management - Edit', 'Edit permission details and assignments', 'permission_management', 'edit'),
('Permission Management - Delete', 'Delete permissions', 'permission_management', 'delete'),
('Permission Management - Full Access', 'Full access to permission management features', 'permission_management', 'full_access'),
('Redis Management - View', 'View Redis data and statistics', 'redis_management', 'view'),
('Redis Management - Edit', 'Edit Redis keys and values', 'redis_management', 'edit'),
('Redis Management - Delete', 'Delete Redis keys', 'redis_management', 'delete'),
('Redis Management - Full Access', 'Full access to Redis management features', 'redis_management', 'full_access'),
('Domain Management - View', 'View domain whitelist', 'domain_management', 'view'),
('Domain Management - Add', 'Add domains to whitelist', 'domain_management', 'add'),
('Domain Management - Remove', 'Remove domains from whitelist', 'domain_management', 'remove'),
('Domain Management - Full Access', 'Full access to domain management features', 'domain_management', 'full_access')
ON CONFLICT (name) DO NOTHING;

-- Grant all administrative permissions to Super Admin security group
INSERT INTO security_group_permissions (security_group_id, permission_id)
SELECT g.id, p.id
FROM security_groups g, permissions p
WHERE g.name = 'Super Admin' 
AND p.resource IN ('system_settings', 'user_management', 'group_management', 'permission_management', 'redis_management', 'domain_management')
ON CONFLICT (security_group_id, permission_id) DO NOTHING;

-- Also grant basic permissions to Super Admin (they should have everything)
INSERT INTO security_group_permissions (security_group_id, permission_id)
SELECT g.id, p.id
FROM security_groups g, permissions p
WHERE g.name = 'Super Admin' 
AND p.resource IN ('system', 'profile', 'data')
AND p.action = 'read'
ON CONFLICT (security_group_id, permission_id) DO NOTHING;

-- Insert default system settings
INSERT INTO system_settings (setting_key, setting_value, data_type, description, category) VALUES 
('app_name', 'Web Dashboard', 'string', 'Application name displayed in the UI', 'general'),
('app_version', '1.0.0', 'string', 'Current application version', 'general'),
('maintenance_mode', 'false', 'boolean', 'Enable or disable maintenance mode', 'general'),
('max_login_attempts', '5', 'integer', 'Maximum failed login attempts before account lockout', 'security'),
('session_timeout_hours', '24', 'integer', 'Session timeout in hours', 'security'),
('email_notifications_enabled', 'true', 'boolean', 'Enable or disable email notifications', 'email'),
('recaptcha_enabled', 'false', 'boolean', 'Enable reCAPTCHA for login forms', 'security'),
('site_url', 'https://abf82530-f459-4235-b523-4961d3b9ea50-00-3p2c1vyhrggly.kirk.replit.dev:5000', 'string', 'Base URL for the website (e.g., https://mydomain.com)', 'general'),
('session_timeout', '86400', 'integer', 'Session timeout in seconds (default: 24 hours)', 'security'),
('password_min_length', '8', 'integer', 'Minimum password length requirement', 'security'),
('password_require_special', 'true', 'boolean', 'Require special characters in passwords', 'security'),
('email_from_address', '', 'string', 'Default from email address', 'email'),
('email_from_name', 'Web Dashboard', 'string', 'Default from name for emails', 'email'),
('api_rate_limit', '100', 'integer', 'API requests per minute per user', 'api'),
('debug_level', 'INFO', 'string', 'System debug logging level (TRACE, DEBUG, INFO, WARN, ERROR)', 'system')
ON CONFLICT (setting_key) DO NOTHING;