
name: Tests

on:
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: '1.23'
    
    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
    
    - name: Install Go dependencies
      working-directory: ./web-dashboard/backend
      run: go mod download
    
    - name: Install frontend dependencies
      working-directory: ./web-dashboard/frontend
      run: npm install
    
    - name: Build frontend
      working-directory: ./web-dashboard/frontend
      run: npm run build
    
    - name: Run Go tests
      working-directory: ./web-dashboard/backend
      env:
        DB_TYPE: sqlite
        DB_PATH: ":memory:"
        REDIS_URL: redis://localhost:6379
        SESSION_KEY: test-session-key-32-bytes-long-for-testing
      run: |
        go test -v -coverprofile=coverage.out ./...
    
    - name: Generate coverage report
      working-directory: ./web-dashboard/backend
      run: go tool cover -func=coverage.out
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./web-dashboard/backend/coverage.out
        flags: backend
        name: backend-coverage
    
    - name: Run race detection tests
      working-directory: ./web-dashboard/backend
      env:
        DB_TYPE: sqlite
        DB_PATH: ":memory:"
        REDIS_URL: redis://localhost:6379
        SESSION_KEY: test-session-key-32-bytes-long-for-testing
      run: go test -race -short ./...
    
    - name: Run benchmark tests
      working-directory: ./web-dashboard/backend
      env:
        DB_TYPE: sqlite
        DB_PATH: ":memory:"
        REDIS_URL: redis://localhost:6379
        SESSION_KEY: test-session-key-32-bytes-long-for-testing
      run: go test -bench=. -benchtime=1s ./...
