
.PHONY: test test-verbose test-coverage test-race clean-test

# Run all tests
test:
	go test ./...

# Run tests with verbose output
test-verbose:
	go test -v ./...

# Run tests with detailed output and timing
test-detailed:
	go test -v -count=1 -timeout=30s ./...

# Run tests with JSON output for detailed analysis
test-json:
	go test -v -json ./...

# Run comprehensive detailed test report
test-report:
	./run_detailed_tests.sh

# Run tests with coverage
test-coverage:
	go test -coverprofile=coverage.out ./...
	go tool cover -html=coverage.out -o coverage.html

# Run tests with race detection
test-race:
	go test -race ./...

# Clean test cache and coverage files
clean-test:
	go clean -testcache
	rm -f coverage.out coverage.html

# Run specific test
test-specific:
	@if [ -z "$(TEST)" ]; then \
		echo "Usage: make test-specific TEST=TestFunctionName"; \
	else \
		go test -v -run $(TEST) ./...; \
	fi

# Run tests for specific package
test-package:
	@if [ -z "$(PKG)" ]; then \
		echo "Usage: make test-package PKG=package_name"; \
	else \
		go test -v ./$(PKG)/...; \
	fi

# Benchmark tests
benchmark:
	go test -bench=. ./...

# All quality checks
quality: test-race test-coverage
	@echo "All quality checks passed!"
