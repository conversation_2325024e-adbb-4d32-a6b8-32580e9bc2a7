{"name": "svelte-app", "version": "1.0.0", "private": true, "type": "module", "scripts": {"build": "rollup -c", "dev": "rollup -c -w", "start": "sirv public --no-clear"}, "devDependencies": {"@rollup/plugin-commonjs": "^24.1.0", "@rollup/plugin-node-resolve": "^15.0.0", "@rollup/plugin-replace": "^5.0.7", "@rollup/plugin-terser": "^0.4.0", "rollup": "^3.15.0", "rollup-plugin-css-only": "^4.3.0", "rollup-plugin-livereload": "^2.0.0", "rollup-plugin-svelte": "^7.1.2", "svelte": "^4.2.19"}, "dependencies": {"sirv-cli": "^2.0.0", "svelte-routing": "^2.13.0", "svelte-spa-router": "^4.0.1", "tabulator-tables": "^6.3.0", "wx-svelte-grid": "^1.3.3"}}