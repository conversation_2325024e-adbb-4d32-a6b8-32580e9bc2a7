
package config

import (
	"encoding/json"
	"os"
	"strings"
)

// LogLevel represents different logging levels
type LogLevel int

const (
	TRACE LogLevel = iota
	DEBUG
	INFO
	WARN
	ERROR
)

// String returns the string representation of the log level
func (l LogLevel) String() string {
	switch l {
	case TRACE:
		return "TRACE"
	case DEBUG:
		return "DEBUG"
	case INFO:
		return "INFO"
	case WARN:
		return "WARN"
	case ERROR:
		return "ERROR"
	default:
		return "UNKNOWN"
	}
}

// SystemSettingFetcher is a function type for fetching system settings from database
type SystemSettingFetcher func(key string) (string, error)

// CacheManager interface for Redis operations
type CacheManager interface {
	GetSystemSettingFromCache(key string) (string, error)
	SetSystemSettingCache(key, value string) error
}

// DebugConfig manages debug configuration
type DebugConfig struct {
	cacheManager       CacheManager
	systemSettingFetcher SystemSettingFetcher
}

var debugConfig *DebugConfig

// InitDebugConfig initializes the debug configuration
func InitDebugConfig(cacheManager CacheManager, fetcher SystemSettingFetcher) {
	debugConfig = &DebugConfig{
		cacheManager:         cacheManager,
		systemSettingFetcher: fetcher,
	}
}

// GetDebugLevel returns the current debug level
func GetDebugLevel() LogLevel {
	// Check environment variable first (highest priority)
	if envLevel := os.Getenv("LOG_LEVEL"); envLevel != "" {
		if level := parseLogLevel(envLevel); level != -1 {
			return level
		}
	}

	// Check Redis cache if cache manager is available
	if debugConfig != nil && debugConfig.cacheManager != nil {
		if redisLevel := getDebugLevelFromRedis(); redisLevel != -1 {
			return redisLevel
		}
	}

	// Default to INFO if not set
	return INFO
}

// getDebugLevelFromRedis retrieves debug level from Redis cache or database
func getDebugLevelFromRedis() LogLevel {
	if debugConfig == nil || debugConfig.cacheManager == nil {
		return -1
	}

	// Try cache first
	cachedValue, err := debugConfig.cacheManager.GetSystemSettingFromCache("debug_level")
	if err == nil {
		// Parse cached value
		if level := parseDebugLevelFromJSON(cachedValue); level != -1 {
			return level
		}
	}

	// Cache miss - fetch from database if fetcher is available
	if debugConfig.systemSettingFetcher != nil {
		if dbValue, err := debugConfig.systemSettingFetcher("debug_level"); err == nil {
			// Cache the fresh value
			if debugConfig.cacheManager != nil {
				debugConfig.cacheManager.SetSystemSettingCache("debug_level", dbValue)
			}
			// Parse database value
			if level := parseDebugLevelFromJSON(dbValue); level != -1 {
				return level
			}
		}
	}

	return -1
}

// parseDebugLevelFromJSON extracts debug level from JSON system setting
func parseDebugLevelFromJSON(jsonValue string) LogLevel {
	// Parse the JSON to get the actual value
	// Expected format: {"id":1,"key":"debug_level","value":"DEBUG","type":"string"}
	var setting struct {
		Value string `json:"value"`
	}
	
	if err := json.Unmarshal([]byte(jsonValue), &setting); err != nil {
		// Fallback to string parsing for simple values
		valueStart := strings.Index(jsonValue, `"value":"`)
		if valueStart == -1 {
			return -1
		}
		valueStart += 9 // Length of `"value":"`

		valueEnd := strings.Index(jsonValue[valueStart:], `"`)
		if valueEnd == -1 {
			return -1
		}

		debugLevelValue := jsonValue[valueStart : valueStart+valueEnd]
		return parseLogLevel(debugLevelValue)
	}
	
	return parseLogLevel(setting.Value)
}

// parseLogLevel converts string to LogLevel
func parseLogLevel(level string) LogLevel {
	switch strings.ToUpper(level) {
	case "TRACE":
		return TRACE
	case "DEBUG":
		return DEBUG
	case "INFO":
		return INFO
	case "WARN", "WARNING":
		return WARN
	case "ERROR":
		return ERROR
	default:
		return -1
	}
}

// ShouldLog determines if a message at the given level should be logged
func ShouldLog(level LogLevel) bool {
	currentLevel := GetDebugLevel()
	return level >= currentLevel
}

// GetCurrentLogLevelString returns the current effective log level as string
func GetCurrentLogLevelString() string {
	return GetDebugLevel().String()
}
