// Adding debug import to the system_settings package.
package system_settings

import (
	"encoding/json"
	"fmt"
	"strconv"

	"web-dashboard/backend/database"
)

type SystemSetting struct {
	ID          int         `json:"id"`
	Key         string      `json:"key"`
	Value       interface{} `json:"value"`
	DataType    string      `json:"data_type"`
	Description string      `json:"description"`
	Category    string      `json:"category"`
	IsEncrypted bool        `json:"is_encrypted"`
	CreatedAt   string      `json:"created_at"`
	UpdatedAt   string      `json:"updated_at"`
}

// GetSystemSetting retrieves a single system setting by key
func GetSystemSetting(key string) (*SystemSetting, error) {
	dbSetting, err := database.GetSystemSetting(key)
	if err != nil {
		return nil, err
	}

	// Convert database setting to business logic setting with proper value type
	setting := &SystemSetting{
		ID:          dbSetting.ID,
		Key:         dbSetting.Key,
		DataType:    dbSetting.DataType,
		Description: dbSetting.Description,
		Category:    dbSetting.Category,
		IsEncrypted: dbSetting.IsEncrypted,
		CreatedAt:   dbSetting.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:   dbSetting.UpdatedAt.Format("2006-01-02 15:04:05"),
	}

	// Convert value based on data type
	setting.Value = convertValueFromString(dbSetting.Value, dbSetting.DataType)

	return setting, nil
}

// GetAllSystemSettings retrieves all system settings
func GetAllSystemSettings() ([]SystemSetting, error) {
	dbSettings, err := database.GetAllSystemSettings()
	if err != nil {
		return nil, err
	}

	settings := make([]SystemSetting, len(dbSettings))
	for i, dbSetting := range dbSettings {
		settings[i] = SystemSetting{
			ID:          dbSetting.ID,
			Key:         dbSetting.Key,
			Value:       convertValueFromString(dbSetting.Value, dbSetting.DataType),
			DataType:    dbSetting.DataType,
			Description: dbSetting.Description,
			Category:    dbSetting.Category,
			IsEncrypted: dbSetting.IsEncrypted,
			CreatedAt:   dbSetting.CreatedAt.Format("2006-01-02 15:04:05"),
			UpdatedAt:   dbSetting.UpdatedAt.Format("2006-01-02 15:04:05"),
		}
	}

	return settings, nil
}

// GetSystemSettingsByCategory retrieves settings by category
func GetSystemSettingsByCategory(category string) ([]SystemSetting, error) {
	dbSettings, err := database.GetSystemSettingsByCategory(category)
	if err != nil {
		return nil, err
	}

	settings := make([]SystemSetting, len(dbSettings))
	for i, dbSetting := range dbSettings {
		settings[i] = SystemSetting{
			ID:          dbSetting.ID,
			Key:         dbSetting.Key,
			Value:       convertValueFromString(dbSetting.Value, dbSetting.DataType),
			DataType:    dbSetting.DataType,
			Description: dbSetting.Description,
			Category:    dbSetting.Category,
			IsEncrypted: dbSetting.IsEncrypted,
			CreatedAt:   dbSetting.CreatedAt.Format("2006-01-02 15:04:05"),
			UpdatedAt:   dbSetting.UpdatedAt.Format("2006-01-02 15:04:05"),
		}
	}

	return settings, nil
}

// CreateOrUpdateSystemSetting creates or updates a system setting
func CreateOrUpdateSystemSetting(key string, value interface{}, category, valueType string) error {
	// Convert value to string for storage
	valueStr, err := convertValueToString(value, valueType)
	if err != nil {
		return fmt.Errorf("failed to convert value: %v", err)
	}

	// Check if setting exists to preserve description
	existingSetting, err := database.GetSystemSetting(key)
	description := ""
	isEncrypted := false

	if err == nil {
		// Setting exists, preserve description and encryption flag
		description = existingSetting.Description
		isEncrypted = existingSetting.IsEncrypted
	}

	return database.SetSystemSetting(key, valueStr, valueType, description, category, isEncrypted)
}

// UpdateSystemSetting updates an existing system setting
func UpdateSystemSetting(key string, value interface{}, category, valueType string) error {
	// First check if the setting exists and get current values
	existingSetting, err := database.GetSystemSetting(key)
	if err != nil {
		return fmt.Errorf("setting not found: %v", err)
	}

	// Convert value to string for storage
	valueStr, err := convertValueToString(value, valueType)
	if err != nil {
		return fmt.Errorf("failed to convert value: %v", err)
	}

	// Preserve existing description and encryption flag
	return database.SetSystemSetting(key, valueStr, valueType, existingSetting.Description, category, existingSetting.IsEncrypted)
}

// DeleteSystemSetting removes a system setting
func DeleteSystemSetting(key string) error {
	return database.DeleteSystemSetting(key)
}

// GetSettingValue retrieves and converts a setting value to the appropriate type
func GetSettingValue(key string) (interface{}, error) {
	return database.GetSettingValue(key)
}

// Convenience functions for specific types
func GetSettingString(key, defaultValue string) string {
	return database.GetSettingString(key, defaultValue)
}

func GetSettingInt(key string, defaultValue int) int {
	return database.GetSettingInt(key, defaultValue)
}

func GetSettingBool(key string, defaultValue bool) bool {
	return database.GetSettingBool(key, defaultValue)
}

// Helper functions for value conversion
// convertValueFromString converts string values to appropriate types based on data_type
func convertValueFromString(value, dataType string) interface{} {
	switch dataType {
	case "string":
		return value
	case "integer":
		if intVal, err := strconv.Atoi(value); err == nil {
			return intVal
		}
		return value
	case "boolean":
		if boolVal, err := strconv.ParseBool(value); err == nil {
			return boolVal
		}
		return value
	case "float":
		if floatVal, err := strconv.ParseFloat(value, 64); err == nil {
			return floatVal
		}
		return value
	case "json":
		// For JSON, return the raw string for the API to handle type assertions
		return value
	default:
		return value
	}
}

func convertValueToString(value interface{}, dataType string) (string, error) {
	switch dataType {
	case "string":
		if str, ok := value.(string); ok {
			return str, nil
		}
		return fmt.Sprintf("%v", value), nil
	case "integer":
		if intVal, ok := value.(int); ok {
			return strconv.Itoa(intVal), nil
		}
		if floatVal, ok := value.(float64); ok {
			return strconv.Itoa(int(floatVal)), nil
		}
		return fmt.Sprintf("%v", value), nil
	case "boolean":
		if boolVal, ok := value.(bool); ok {
			return strconv.FormatBool(boolVal), nil
		}
		return fmt.Sprintf("%v", value), nil
	case "float":
		if floatVal, ok := value.(float64); ok {
			return strconv.FormatFloat(floatVal, 'f', -1, 64), nil
		}
		if intVal, ok := value.(int); ok {
			return strconv.FormatFloat(float64(intVal), 'f', -1, 64), nil
		}
		return fmt.Sprintf("%v", value), nil
	case "json":
		jsonBytes, err := json.Marshal(value)
		if err != nil {
			return "", err
		}
		return string(jsonBytes), nil
	default:
		return fmt.Sprintf("%v", value), nil
	}
}