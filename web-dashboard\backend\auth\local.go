package auth

import (
	"context"
	"crypto/rand"
	"crypto/subtle"
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strings"
	"sync"
	"time"

	"web-dashboard/backend/database"
	"web-dashboard/backend/debug"
	"web-dashboard/backend/email"
	"web-dashboard/backend/session"
	"web-dashboard/backend/users"

	"golang.org/x/crypto/argon2"
	"golang.org/x/crypto/bcrypt"
)

// BruteForceProtection manages rate limiting for reset token attempts
type BruteForceProtection struct {
	attempts map[string]*AttemptInfo
	mutex    sync.RWMutex
}

type AttemptInfo struct {
	count     int
	lastAttempt time.Time
	blockedUntil time.Time
}

var resetTokenBruteForce = &BruteForceProtection{
	attempts: make(map[string]*AttemptInfo),
}

// checkResetTokenRateLimit checks if the IP is rate limited for reset token attempts
func (bp *BruteForceProtection) checkResetTokenRateLimit(clientIP string) (bool, time.Duration) {
	bp.mutex.Lock()
	defer bp.mutex.Unlock()

	now := time.Now()

	// Clean up old entries (older than 24 hours)
	for ip, info := range bp.attempts {
		if now.Sub(info.lastAttempt) > 24*time.Hour && now.After(info.blockedUntil) {
			delete(bp.attempts, ip)
		}
	}

	info, exists := bp.attempts[clientIP]
	if !exists {
		bp.attempts[clientIP] = &AttemptInfo{
			count:       0,
			lastAttempt: now,
			blockedUntil: time.Time{},
		}
		return true, 0
	}

	// Check if currently blocked
	if now.Before(info.blockedUntil) {
		return false, info.blockedUntil.Sub(now)
	}

	// Reset counter if last attempt was more than 1 hour ago
	if now.Sub(info.lastAttempt) > time.Hour {
		info.count = 0
	}

	return true, 0
}

// recordResetTokenAttempt records a failed reset token attempt
func (bp *BruteForceProtection) recordResetTokenAttempt(clientIP string, success bool) {
	bp.mutex.Lock()
	defer bp.mutex.Unlock()

	now := time.Now()
	info, exists := bp.attempts[clientIP]
	if !exists {
		info = &AttemptInfo{}
		bp.attempts[clientIP] = info
	}

	info.lastAttempt = now
	debug.Debug("Recording reset token attempt for IP %s: success=%v, current_count=%d", clientIP, success, info.count)

	if success {
		// Reset counter on successful attempt
		info.count = 0
		info.blockedUntil = time.Time{}
		debug.Debug("Reset successful, cleared counter for IP %s", clientIP)
		return
	}

	// Increment failed attempts
	info.count++
	debug.Debug("Failed attempt recorded for IP %s, new count: %d", clientIP, info.count)

	// Apply progressive timeouts
	switch {
	case info.count >= 20:
		// 20+ attempts: 24 hour block
		info.blockedUntil = now.Add(24 * time.Hour)
		debug.Warn("SECURITY: IP %s blocked for 24 hours after %d reset token attempts", clientIP, info.count)
	case info.count >= 10:
		// 10-19 attempts: 1 hour block
		info.blockedUntil = now.Add(time.Hour)
		debug.Warn("SECURITY: IP %s blocked for 1 hour after %d reset token attempts", clientIP, info.count)
	case info.count >= 5:
		// 5-9 attempts: 15 minute block
		info.blockedUntil = now.Add(15 * time.Minute)
		debug.Warn("SECURITY: IP %s blocked for 15 minutes after %d reset token attempts", clientIP, info.count)
	case info.count >= 3:
		// 3-4 attempts: 5 minute block
		info.blockedUntil = now.Add(5 * time.Minute)
		debug.Warn("SECURITY: IP %s blocked for 5 minutes after %d reset token attempts", clientIP, info.count)
	}
}

// getClientIP extracts the client IP address from the request
func getClientIP(r *http.Request) string {
	// Try X-Forwarded-For header first (for proxies)
	if xff := r.Header.Get("X-Forwarded-For"); xff != "" {
		// Take the first IP in the list
		if idx := strings.Index(xff, ","); idx != -1 {
			return strings.TrimSpace(xff[:idx])
		}
		return strings.TrimSpace(xff)
	}

	// Try X-Real-IP header
	if xri := r.Header.Get("X-Real-IP"); xri != "" {
		return strings.TrimSpace(xri)
	}

	// Fall back to RemoteAddr
	if idx := strings.LastIndex(r.RemoteAddr, ":"); idx != -1 {
		return r.RemoteAddr[:idx]
	}
	return r.RemoteAddr
}

// VerifyPassword verifies a password against its hash
func VerifyPassword(password, hash string) bool {
	var salt, storedHash []byte
	if _, err := fmt.Sscanf(hash, "%x:%x", &salt, &storedHash); err != nil {
		return false
	}

	// Hash the provided password with the stored salt
	passwordHash := argon2.IDKey([]byte(password), salt, 1, 64*1024, 4, 32)

	// Compare the hashes
	return subtle.ConstantTimeCompare(passwordHash, storedHash) == 1
}

// LocalLoginHandler handles user login
func LocalLoginHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != "POST" {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	email := r.FormValue("email")
	password := r.FormValue("password")
	recaptchaToken := r.FormValue("recaptcha_token")

	if email == "" || password == "" {
		http.Error(w, "Email and password are required", http.StatusBadRequest)
		return
	}

	// Verify reCAPTCHA v3 for login
	if getRecaptchaEnabledFromSettings() {
		if recaptchaToken == "" {
			http.Error(w, "reCAPTCHA verification required", http.StatusBadRequest)
			return
		}

		isValid, err := verifyRecaptchaWithAction(recaptchaToken, "login")
		if err != nil {
			log.Printf("reCAPTCHA verification error: %v", err)
			http.Error(w, "reCAPTCHA verification failed", http.StatusInternalServerError)
			return
		}

		if !isValid {
			http.Error(w, "reCAPTCHA verification failed. Please try again.", http.StatusBadRequest)
			return
		}
	}

	// Get user from database with password hash for authentication
	user, err := users.GetUserByEmailWithPassword(email)
	if err != nil {
		if err.Error() == "user not found" || err.Error() == "sql: no rows in result set" {
			http.Error(w, "Invalid credentials", http.StatusUnauthorized)
			return
		}
		log.Printf("DEBUG: LocalLoginHandler - Database error: %v\n", err)
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}

	// Check if user object is nil
	if user == nil {
		http.Error(w, "Invalid credentials", http.StatusUnauthorized)
		return
	}

	// Check if account is enabled
	if !user.Enabled {
		http.Error(w, "Account is disabled. Please contact an administrator.", http.StatusForbidden)
		return
	}

	// Verify password
	debug.Debug("LocalLoginHandler - Verifying password for user %s", user.Email)
	debug.Debug("LocalLoginHandler - Password hash length: %d", len(user.PasswordHash))
	if !VerifyPassword(password, user.PasswordHash) {
		debug.Warn("LocalLoginHandler - Password verification failed for user %s", user.Email)
		http.Error(w, "Invalid credentials", http.StatusUnauthorized)
		return
	}
	debug.Debug("LocalLoginHandler - Password verification successful for user %s", user.Email)

	// Check if TOTP is required
	totpRequired, err := RequireTOTP(user.ID)
	if err != nil {
		log.Printf("Error checking TOTP requirement: %v", err)
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}

	if totpRequired {
		totpCode := r.FormValue("totp_code")
		if totpCode == "" {
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)
			json.NewEncoder(w).Encode(map[string]interface{}{
				"requires_totp": true,
				"message":       "TOTP code required",
			})
			return
		}

		// Validate TOTP code
		valid, err := ValidateTOTP(user.ID, totpCode)
		if err != nil {
			log.Printf("Error validating TOTP: %v", err)
			http.Error(w, "Invalid TOTP code", http.StatusUnauthorized)
			return
		}

		if !valid {
			http.Error(w, "Invalid TOTP code", http.StatusUnauthorized)
			return
		}
	}

	// Update last login info
	now := time.Now().UTC()
	updateQuery := `UPDATE users SET last_login_method = 'local', last_login_at = $1, updated_at = $1 WHERE id = $2`
	_, err = database.DB.Exec(updateQuery, now, user.ID)
	if err != nil {
		log.Printf("Error updating last login: %v", err)
		// Don't fail the login for this
	}

	// Generate session ID for Redis
	sessionID, err := generateSessionID()
	if err != nil {
		log.Printf("Error generating session ID: %v", err)
		http.Error(w, "Failed to generate session ID", http.StatusInternalServerError)
		return
	}

	// Get user permissions
	permissions, err := session.Manager.GetUserPermissions(user.Email)
	if err != nil {
		log.Printf("WARNING: Failed to get permissions for %s: %v\n", user.Email, err)
		permissions = []string{"Read Access"} // Default permission
	}

	// Get client IP address
	clientIP := session.GetClientIP(r)

	// Check if session manager is initialized
	if session.Manager == nil {
		log.Printf("Session manager not initialized")
		http.Error(w, "Session service unavailable", http.StatusInternalServerError)
		return
	}

	// Create Redis session
	err = session.Manager.CreateSession(sessionID, user.ID, user.Email, "local", permissions, clientIP)
	if err != nil {
		log.Printf("Failed to create Redis session: %v", err)
		http.Error(w, "Failed to create session", http.StatusInternalServerError)
		return
	}

	// Explicitly update IP history to ensure it's recorded
	if clientIP != "" {
		err = session.Manager.UpdateUserIPHistory(user.ID, clientIP)
		if err != nil {
			log.Printf("Warning: Failed to update IP history for user %s: %v", user.Email, err)
		} else {
			log.Printf("Successfully updated IP history for user %s with IP %s", user.Email, clientIP)
		}
	}

	// Set session cookie
	cookie := &http.Cookie{
		Name:     "session_token",
		Value:    sessionID,
		Path:     "/",
		MaxAge:   86400 * 7, // 7 days
		HttpOnly: true,
		Secure:   false, // Set to true in production with HTTPS
		SameSite: http.SameSiteLaxMode,
	}
	http.SetCookie(w, cookie)

	log.Printf("Redis session created successfully for local user: %s", user.Email)

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"success": true,
		"message": "Login successful",
	})
}

// ChangePasswordHandler handles password change requests
func ChangePasswordHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != "POST" {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Get current user from session
	currentUser, err := GetCurrentUserEmail(r)
	if err != nil {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	currentPassword := r.FormValue("current_password")
	newPassword := r.FormValue("new_password")

	if currentPassword == "" || newPassword == "" {
		http.Error(w, "Current password and new password are required", http.StatusBadRequest)
		return
	}

	// Get user's current password hash
	var storedPasswordHash string
	query := `SELECT password_hash FROM users WHERE email = $1`
	err = database.DB.QueryRow(query, currentUser).Scan(&storedPasswordHash)
	if err != nil {
		log.Printf("Error getting user password: %v", err)
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}

	// Verify current password
	if !VerifyPassword(currentPassword, storedPasswordHash) {
		http.Error(w, "Current password is incorrect", http.StatusUnauthorized)
		return
	}

	// Hash new password
	hashedNewPassword, err := HashPassword(newPassword)
	if err != nil {
		debug.Error("Error hashing new password: %v", err)
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}

	// Update password in database
	now := time.Now().UTC()
	updateQuery := `UPDATE users SET password_hash = $1, updated_at = $2 WHERE email = $3`
	_, err = database.DB.Exec(updateQuery, hashedNewPassword, now, currentUser)
	if err != nil {
		debug.Error("Error updating password: %v", err)
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"success": true,
		"message": "Password updated successfully",
	})
}

// RequestPasswordResetHandler handles password reset requests
func RequestPasswordResetHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != "POST" {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Get current user from session
	currentUser, err := GetCurrentUserEmail(r)
	if err != nil {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	// Generate a secure reset token
	resetToken, err := generateSecureToken()
	if err != nil {
		log.Printf("Error generating reset token: %v", err)
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}

	// Store reset token in database with expiration (24 hours)
	expiresAt := time.Now().Add(24 * time.Hour)
	query := `INSERT INTO password_reset_tokens (email, token, expires_at, created_at) 
              VALUES ($1, $2, $3, $4)
              ON CONFLICT (email) 
              DO UPDATE SET token = $2, expires_at = $3, created_at = $4`

	_, err = database.DB.Exec(query, currentUser, resetToken, expiresAt, time.Now())
	if err != nil {
		log.Printf("Error storing reset token: %v", err)
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}

	// Send password reset email
	err = sendPasswordResetEmail(currentUser, resetToken)
	if err != nil {
		log.Printf("Error sending reset email: %v", err)
		http.Error(w, "Failed to send reset email", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"success": true,
		"message": "Password reset email sent successfully",
	})
}

// ResetPasswordHandler handles password reset with token
func ResetPasswordHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != "POST" {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Get client IP for rate limiting
	clientIP := getClientIP(r)
	debug.Debug("Reset password attempt from IP: %s", clientIP)

	// Check rate limiting
	allowed, waitTime := resetTokenBruteForce.checkResetTokenRateLimit(clientIP)
	debug.Debug("Rate limit check for IP %s: allowed=%v, waitTime=%v", clientIP, allowed, waitTime)
	if !allowed {
		debug.Warn("SECURITY: Reset token attempt blocked for IP %s (blocked for %v)", clientIP, waitTime)
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusTooManyRequests)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"error": "rate_limited",
			"message": fmt.Sprintf("Too many reset attempts. Please try again in %v.", waitTime.Round(time.Minute)),
			"retry_after": int(waitTime.Seconds()),
		})
		return
	}

	token := r.FormValue("token")
	newPassword := r.FormValue("new_password")

	if token == "" || newPassword == "" {
		// Record failed attempt for missing parameters
		resetTokenBruteForce.recordResetTokenAttempt(clientIP, false)
		http.Error(w, "Token and new password are required", http.StatusBadRequest)
		return
	}

	// Verify token and get email
	var email string
	var expiresAt time.Time
	query := `SELECT email, expires_at FROM password_reset_tokens WHERE token = $1`
	err := database.DB.QueryRow(query, token).Scan(&email, &expiresAt)
	if err != nil {
		// Record failed attempt for invalid token
		resetTokenBruteForce.recordResetTokenAttempt(clientIP, false)

		if err == sql.ErrNoRows {
			debug.Warn("SECURITY: Invalid reset token attempt from IP %s", clientIP)
			http.Error(w, "Invalid or expired reset token", http.StatusBadRequest)
			return
		}
		debug.Error("Error verifying reset token from IP %s: %v", clientIP, err)
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}

	// Check if token is expired
	if time.Now().After(expiresAt) {
		// Record failed attempt for expired token
		resetTokenBruteForce.recordResetTokenAttempt(clientIP, false)
		debug.Warn("SECURITY: Expired reset token attempt from IP %s", clientIP)
		http.Error(w, "Reset token has expired", http.StatusBadRequest)
		return
	}

	// Hash new password
	hashedNewPassword, err := HashPassword(newPassword)
	if err != nil {
		debug.Error("Error hashing new password: %v", err)
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}

	// Update password in database
	now := time.Now().UTC()
	updateQuery := `UPDATE users SET password_hash = $1, updated_at = $2 WHERE email = $3`
	_, err = database.DB.Exec(updateQuery, hashedNewPassword, now, email)
	if err != nil {
		debug.Error("Error updating password: %v", err)
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}

	// Delete the used reset token
	deleteQuery := `DELETE FROM password_reset_tokens WHERE token = $1`
	_, err = database.DB.Exec(deleteQuery, token)
	if err != nil {
		log.Printf("Warning: Failed to delete used reset token: %v", err)
	}

	// Record successful attempt (this will reset the counter)
	resetTokenBruteForce.recordResetTokenAttempt(clientIP, true)
	debug.Info("Successful password reset for %s from IP %s", email, clientIP)

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"success": true,
		"message": "Password reset successfully",
	})
}

// ResetPasswordFormHandler handles GET requests for the reset password form with token validation
func ResetPasswordFormHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != "GET" {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	token := r.URL.Query().Get("token")
	if token == "" {
		// No token provided, just serve the form
		w.Header().Set("Content-Type", "text/html; charset=utf-8")
		http.ServeFile(w, r, "../frontend/public/index.html")
		return
	}

	// Get client IP for rate limiting
	clientIP := getClientIP(r)
	log.Printf("DEBUG: Reset password form request from IP: %s with token", clientIP)

	// Check rate limiting for token validation
	allowed, waitTime := resetTokenBruteForce.checkResetTokenRateLimit(clientIP)
	log.Printf("DEBUG: Rate limit check for IP %s: allowed=%v, waitTime=%v", clientIP, allowed, waitTime)
	if !allowed {
		log.Printf("SECURITY: Reset token form access blocked for IP %s (blocked for %v)", clientIP, waitTime)
		// Serve the reset page with error parameters instead of redirecting
		w.Header().Set("Content-Type", "text/html; charset=utf-8")
		http.ServeFile(w, r, "../frontend/public/index.html")
		return
	}

	// Verify token exists and is not expired
	var email string
	var expiresAt time.Time
	query := `SELECT email, expires_at FROM password_reset_tokens WHERE token = $1`
	err := database.DB.QueryRow(query, token).Scan(&email, &expiresAt)
	if err != nil {
		// Record failed attempt for invalid token
		resetTokenBruteForce.recordResetTokenAttempt(clientIP, false)

		if err == sql.ErrNoRows {
			log.Printf("SECURITY: Invalid reset token in GET request from IP %s", clientIP)
			http.Error(w, "Invalid reset token", http.StatusBadRequest)
			return
		}
		log.Printf("Error verifying reset token in GET request from IP %s: %v", clientIP, err)
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}

	// Check if token is expired
	if time.Now().After(expiresAt) {
		// Record failed attempt for expired token
		resetTokenBruteForce.recordResetTokenAttempt(clientIP, false)
		log.Printf("SECURITY: Expired reset token in GET request from IP %s", clientIP)
		
		// Redirect to reset password page with error instead of serving the form
		http.Redirect(w, r, "/reset-password?error=expired&message=This+reset+link+has+expired.+Please+request+a+new+password+reset.", http.StatusFound)
		return
	}

	// Token is valid, serve the reset form
	log.Printf("Valid reset token accessed from IP %s for email %s", clientIP, email)
	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	http.ServeFile(w, r, "../frontend/public/index.html")
}

// LoginHandler handles user login
func LoginHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != "POST" {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	email := r.FormValue("email")
	password := r.FormValue("password")

	if email == "" || password == "" {
		http.Error(w, "Email and password are required", http.StatusBadRequest)
		return
	}

	// Retrieve the user from the database by email
	user, err := users.GetUserByEmailWithPassword(email)
	if err != nil {
		http.Error(w, "Invalid credentials", http.StatusUnauthorized)
		return
	}

	// Check if user is enabled
	if !user.Enabled {
		http.Error(w, "Account is disabled. Please contact an administrator.", http.StatusForbidden)
		return
	}

	// Compare the provided password with the hashed password
	err = bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(password))
	if err != nil {
		http.Error(w, "Invalid credentials", http.StatusUnauthorized)
		return
	}

	// Create a session token
	sessionToken, err := generateSecureToken()
	if err != nil {
		fmt.Printf("Error generating session token: %v\n", err)
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}

	// Store the session token in the database
	err = CreateSession(user.ID, sessionToken)
	if err != nil {
		fmt.Printf("Error creating session: %v\n", err)
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}

	// Set the session token as a cookie
	http.SetCookie(w, &http.Cookie{
		Name:     "session_token",
		Value:    sessionToken,
		Path:     "/",
		HttpOnly: true,
		Secure:   true, // Set to true in production
		SameSite: http.SameSiteStrictMode,
	})

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"success": true,
		"message": "Login successful",
	})
}

// RequiresLogin middleware to protect routes
func RequiresLogin(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Get the session token from the cookie
		sessionCookie, err := r.Cookie("session_token")
		if err != nil {
			http.Error(w, "Unauthorized", http.StatusUnauthorized)
			return
		}
		sessionToken := sessionCookie.Value

		// Retrieve the session from the database
		session, err := GetSession(sessionToken)
		if err != nil {
			http.Error(w, "Unauthorized", http.StatusUnauthorized)
			return
		}

		// Get the user from the session
		user, err := users.GetUserByID(session.UserID)
		if err != nil {
			http.Error(w, "Unauthorized", http.StatusUnauthorized)
			return
		}

		// Add the user to the request context
		ctx := context.WithValue(r.Context(), "user", user)
		next(w, r.WithContext(ctx))
	}
}

// HashPassword creates a hash of the password using Argon2
func HashPassword(password string) (string, error) {
	// Generate a random salt
	salt := make([]byte, 16)
	if _, err := rand.Read(salt); err != nil {
		return "", err
	}

	// Hash the password
	hash := argon2.IDKey([]byte(password), salt, 1, 64*1024, 4, 32)

	// Encode the salt and hash
	return fmt.Sprintf("%x:%x", salt, hash), nil
}

func generateSecureToken() (string, error) {
	bytes := make([]byte, 32)
	_, err := rand.Read(bytes)
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("%x", bytes), nil
}

// GetUserLoginInfo gets user login information for display including IP history
func GetUserLoginInfo(email string) (map[string]interface{}, error) {
	fmt.Printf("DEBUG: GetUserLoginInfo called for email: %s\n", email)

	if email == "" {
		fmt.Printf("DEBUG: GetUserLoginInfo called with empty email\n")
		return nil, fmt.Errorf("email cannot be empty")
	}

	user, err := users.GetUserByEmailWithPassword(email)
	if err != nil {
		fmt.Printf("DEBUG: GetUserByEmail failed for %s: %v\n", email, err)
		return nil, fmt.Errorf("failed to get user by email: %v", err)
	}

	if user == nil {
		fmt.Printf("DEBUG: GetUserByEmail returned nil user for %s\n", email)
		return nil, fmt.Errorf("user not found")
	}

	// Get IP history from Redis
	var ipHistory []session.IPRecord
	if session.Manager != nil {
		fmt.Printf("DEBUG: Attempting to get IP history for user ID %d (%s)\n", user.ID, email)
		ipHistory, err = session.Manager.GetUserIPHistory(user.ID)
		if err != nil {
			fmt.Printf("DEBUG: Failed to get IP history for user %s: %v\n", email, err)
			ipHistory = []session.IPRecord{} // Default to empty if error
		} else {
			fmt.Printf("DEBUG: Retrieved %d IP history records for user %s\n", len(ipHistory), email)
			for i, record := range ipHistory {
				fmt.Printf("DEBUG: IP History %d: %s at %d\n", i+1, record.IPAddress, record.Timestamp)
			}
		}
	} else {
		fmt.Printf("DEBUG: Session manager is nil, cannot get IP history\n")
		ipHistory = []session.IPRecord{}
	}

	userInfo := map[string]interface{}{
		"id":                user.ID,
		"email":             user.Email,
		"last_login_method": user.LastLoginMethod,
		"last_login_at":     user.LastLoginAt,
		"enabled":           user.Enabled,
		"created_at":        user.CreatedAt,
		"updated_at":        user.UpdatedAt,
		"ip_history":        ipHistory,
	}

	fmt.Printf("DEBUG: Final userInfo IP history: %+v\n", ipHistory)
	for i, record := range ipHistory {
		fmt.Printf("DEBUG: IP Record %d in userInfo: IPAddress=%s, Timestamp=%d\n", i, record.IPAddress, record.Timestamp)
	}

	fmt.Printf("DEBUG: GetUserLoginInfo returning data for %s with %d IP records\n", email, len(ipHistory))
	fmt.Printf("DEBUG: IP History JSON: %+v\n", ipHistory)
	for i, record := range ipHistory {
		fmt.Printf("DEBUG: IP Record %d: IP=%s, Timestamp=%d\n", i, record.IPAddress, record.Timestamp)
	}
	return userInfo, nil
}

// sendPasswordResetEmail sends a password reset email to the user
func sendPasswordResetEmail(userEmail, resetToken string) error {
	// Create Gmail service
	gmailService, err := email.NewGmailService()
	if err != nil {
		return fmt.Errorf("failed to create Gmail service: %v", err)
	}

	// Get site URL from system settings
	siteURL := database.GetSettingString("site_url", "https://web-dashboard-justinstrive.replit.app")
	if siteURL == "" {
		siteURL = "https://web-dashboard-justinstrive.replit.app"
	}

	// Create reset URL
	resetURL := fmt.Sprintf("%s/reset-password?token=%s", strings.TrimSuffix(siteURL, "/"), resetToken)

	// Create email content
	subject := "Password Reset for Web Dashboard"
	body := fmt.Sprintf(`
<!DOCTYPE html>
<html>
<head>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #4285f4; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background-color: #f9f9f9; }
        .button { 
            display: inline-block; 
            padding: 12px 24px; 
            background-color: #4285f4; 
            color: white; 
            text-decoration: none; 
            border-radius: 4px;
            margin: 20px 0;
        }
        .footer { padding: 20px; text-align: center; color: #666, font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Password Reset Request</h1>
        </div>
        <div class="content">
            <p>Hello,</p>
            <p>You have requested to reset your password for your Web Dashboard account.</p>
            <p>Click the button below to reset your password:</p>
            <a href="%s" class="button">Reset Password</a>
            <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
            <p><a href="%s">%s</a></p>
            <p>This link will expire in 24 hours for security reasons.</p>
            <p>If you didn't request this password reset, please ignore this email.</p>
        </div>
        <div class="footer">
            <p>This is an automated message from Web Dashboard.</p>
        </div>
    </div>
</body>
</html>
	`, resetURL, resetURL, resetURL)

	// Send email
	msg := email.EmailMessage{
		To:      []string{userEmail},
		Subject: subject,
		Body:    body,
		IsHTML:  true,
	}

	return gmailService.SendEmail(msg)
}