
package api

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"web-dashboard/backend/testutils"

	"github.com/gorilla/mux"
)

func TestSecurityGroupsRoutes(t *testing.T) {
	testutils.SetupTestEnvironment()

	router := mux.NewRouter()
	AttachSecurityGroupRoutes(router)

	t.Run("GET /security-groups without auth returns 401", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/security-groups", nil)
		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		if rr.Code != http.StatusUnauthorized && rr.Code != http.StatusNotFound {
			t.Errorf("Expected 401 or 404, got %d", rr.Code)
		}
	})
}
