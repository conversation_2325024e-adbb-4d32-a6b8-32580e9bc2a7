package database

import (
	"os"
	"testing"
	"web-dashboard/backend/testutils"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestMain(m *testing.M) {
	// Set up test environment using shared utilities
	testutils.SetupTestEnvironment()

	// Run tests
	code := m.Run()

	// Cleanup
	if DB != nil {
		DB.Close()
	}

	os.Exit(code)
}

func TestInitDB(t *testing.T) {
	// Test database initialization
	err := InitDB()
	require.NoError(t, err, "Database initialization must succeed with test secrets")
	require.NotNil(t, DB, "Database connection must be established")

	// Test connection
	err = DB.Ping()
	require.NoError(t, err, "Database ping must succeed")
}

func TestGetRecords(t *testing.T) {
	if DB == nil {
		require.NoError(t, InitDB(), "Database must be initialized for this test")
	}

	// Test getting records
	records, err := GetRecords(10)

	// Should not error even if no records exist
	require.NoError(t, err)
	require.NotNil(t, records)
}

func TestSystemSettings(t *testing.T) {
	if DB == nil {
		require.NoError(t, InitDB(), "Database must be initialized for this test")
	}

	t.Run("SetAndGetSystemSetting", func(t *testing.T) {
		// Set a test setting
		err := SetSystemSetting("test_key", "test_value", "string", "Test setting", "test", false)
		require.NoError(t, err)

		// Get the setting
		setting, err := GetSystemSetting("test_key")
		require.NoError(t, err)
		assert.Equal(t, "test_key", setting.Key)
		assert.Equal(t, "test_value", setting.Value)
		assert.Equal(t, "string", setting.DataType)

		// Test convenience function
		value := GetSettingString("test_key", "default")
		assert.Equal(t, "test_value", value)

		// Cleanup
		DeleteSystemSetting("test_key")
	})

	t.Run("GetNonExistentSetting", func(t *testing.T) {
		// Test getting non-existent setting
		_, err := GetSystemSetting("non_existent_key")
		assert.Error(t, err)

		// Test convenience function with default
		value := GetSettingString("non_existent_key", "default_value")
		assert.Equal(t, "default_value", value)
	})

	t.Run("GetAllSystemSettings", func(t *testing.T) {
		// This should not error even if no settings exist
		settings, err := GetAllSystemSettings()
		assert.NoError(t, err)
		assert.NotNil(t, settings)
	})
}

func TestRebuildSchema(t *testing.T) {
	if DB == nil {
		require.NoError(t, InitDB(), "Database must be initialized for this test")
	}

	// Test schema rebuild
	err := RebuildSchema("test_schema")
	require.NoError(t, err)

	// Cleanup - drop the test schema
	_, err = DB.Exec("DROP SCHEMA IF EXISTS test_schema CASCADE")
	require.NoError(t, err)
}