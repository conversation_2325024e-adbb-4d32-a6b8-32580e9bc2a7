package api

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"

	"web-dashboard/backend/session"

	"github.com/gorilla/mux"
)

type RedisKeyInfo struct {
	Key   string      `json:"key"`
	Type  string      `json:"type"`
	TTL   int64       `json:"ttl"`
	Value interface{} `json:"value"`
}

type RedisStats struct {
	TotalKeys        int64          `json:"total_keys"`
	UsedMemory       string         `json:"used_memory"`
	ConnectedClients int64          `json:"connected_clients"`
	Sessions         []SessionInfo  `json:"sessions"`
	KeysByType       map[string]int `json:"keys_by_type"`
}

type SessionInfo struct {
	SessionID   string   `json:"session_id"`
	Email       string   `json:"email"`
	AuthType    string   `json:"auth_type"`
	Permissions []string `json:"permissions"`
	CreatedAt   int64    `json:"created_at"`
	LastAccess  int64    `json:"last_access"`
	IPAddress   string   `json:"ip_address"`
}

var requireRedisManagementPermission = RequireRedisManagement

// AttachRedisRoutes attaches all Redis management routes to the router
func AttachRedisRoutes(router *mux.Router) {
	// Get Redis stats and overview
	router.HandleFunc("/stats", requireRedisManagementPermission(getRedisStatsHandler)).Methods("GET")

	// List all keys with optional pattern
	router.HandleFunc("/keys", requireRedisManagementPermission(listRedisKeysHandler)).Methods("GET")

	// Get specific key value
	router.HandleFunc("/key/{key}", requireRedisManagementPermission(getRedisKeyHandler)).Methods("GET")

	// Delete specific key
	router.HandleFunc("/key/{key}", requireRedisManagementPermission(deleteRedisKeyHandler)).Methods("DELETE")

	// List all sessions
	router.HandleFunc("/sessions", requireRedisManagementPermission(listRedisSessionsHandler)).Methods("GET")

	// Delete specific session
	router.HandleFunc("/session/{sessionId}", requireRedisManagementPermission(deleteRedisSessionHandler)).Methods("DELETE")

	// Clear all Redis data
	router.HandleFunc("/flush", requireRedisManagementPermission(flushRedisDataHandler)).Methods("DELETE")
}

func getRedisStatsHandler(w http.ResponseWriter, r *http.Request) {
	client := session.Manager
	if client == nil {
		http.Error(w, "Redis not available", http.StatusInternalServerError)
		return
	}

	ctx := context.Background()

	// Get basic info
	info, err := client.Client.Info(ctx).Result()
	if err != nil {
		http.Error(w, fmt.Sprintf("Failed to get Redis info: %v", err), http.StatusInternalServerError)
		return
	}

	// Parse memory usage and connected clients from info
	var usedMemory string
	var connectedClients int64

	lines := strings.Split(info, "\r\n")
	for _, line := range lines {
		if strings.HasPrefix(line, "used_memory_human:") {
			usedMemory = strings.TrimPrefix(line, "used_memory_human:")
		}
		if strings.HasPrefix(line, "connected_clients:") {
			clientStr := strings.TrimPrefix(line, "connected_clients:")
			connectedClients, _ = strconv.ParseInt(clientStr, 10, 64)
		}
	}

	// Get all keys count
	keys, err := client.Client.Keys(ctx, "*").Result()
	if err != nil {
		http.Error(w, fmt.Sprintf("Failed to get keys: %v", err), http.StatusInternalServerError)
		return
	}

	// Get sessions
	sessionKeys, err := client.Client.Keys(ctx, "session:*").Result()
	if err != nil {
		http.Error(w, fmt.Sprintf("Failed to get session keys: %v", err), http.StatusInternalServerError)
		return
	}

	var sessions []SessionInfo
	keysByType := make(map[string]int)

	for _, key := range keys {
		keyType, err := client.Client.Type(ctx, key).Result()
		if err == nil {
			keysByType[keyType]++
		}
	}

	for _, key := range sessionKeys {
		sessionData, err := session.Manager.GetSessionReadOnly(strings.TrimPrefix(key, "session:"))
		if err == nil {
			sessions = append(sessions, SessionInfo{
				SessionID:   strings.TrimPrefix(key, "session:"),
				Email:       sessionData.Email,
				AuthType:    sessionData.AuthType,
				Permissions: sessionData.Permissions,
				CreatedAt:   sessionData.CreatedAt,
				LastAccess:  sessionData.LastAccess,
				IPAddress:   sessionData.IPAddress,
			})
		}
	}

	stats := RedisStats{
		TotalKeys:        int64(len(keys)),
		UsedMemory:       usedMemory,
		ConnectedClients: connectedClients,
		Sessions:         sessions,
		KeysByType:       keysByType,
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(stats)
}

func listRedisKeysHandler(w http.ResponseWriter, r *http.Request) {
	client := session.Manager
	if client == nil {
		http.Error(w, "Redis not available", http.StatusInternalServerError)
		return
	}

	pattern := r.URL.Query().Get("pattern")
	if pattern == "" {
		pattern = "*"
	}

	ctx := context.Background()
	keys, err := client.Client.Keys(ctx, pattern).Result()
	if err != nil {
		http.Error(w, fmt.Sprintf("Failed to get keys: %v", err), http.StatusInternalServerError)
		return
	}

	var keyInfos []RedisKeyInfo
	for _, key := range keys {
		keyType, err := client.Client.Type(ctx, key).Result()
		if err != nil {
			continue
		}

		ttl, err := client.Client.TTL(ctx, key).Result()
		if err != nil {
			continue
		}

		keyInfo := RedisKeyInfo{
			Key:  key,
			Type: keyType,
			TTL:  int64(ttl.Seconds()),
		}

		// Get value based on type
		switch keyType {
		case "string":
			val, err := client.Client.Get(ctx, key).Result()
			if err == nil {
				keyInfo.Value = val
			}
		case "list":
			val, err := client.Client.LRange(ctx, key, 0, -1).Result()
			if err == nil {
				keyInfo.Value = val
			}
		case "set":
			val, err := client.Client.SMembers(ctx, key).Result()
			if err == nil {
				keyInfo.Value = val
			}
		case "hash":
			val, err := client.Client.HGetAll(ctx, key).Result()
			if err == nil {
				keyInfo.Value = val
			}
		default:
			keyInfo.Value = "Complex type - use Redis CLI for detailed inspection"
		}

		keyInfos = append(keyInfos, keyInfo)
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(keyInfos)
}

func getRedisKeyHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	key := vars["key"]

	client := session.Manager
	if client == nil {
		http.Error(w, "Redis not available", http.StatusInternalServerError)
		return
	}

	ctx := context.Background()

	// Check if key exists
	exists, err := client.Client.Exists(ctx, key).Result()
	if err != nil {
		http.Error(w, fmt.Sprintf("Failed to check key existence: %v", err), http.StatusInternalServerError)
		return
	}

	if exists == 0 {
		http.Error(w, "Key not found", http.StatusNotFound)
		return
	}

	// Get key info
	keyType, err := client.Client.Type(ctx, key).Result()
	if err != nil {
		http.Error(w, fmt.Sprintf("Failed to get key type: %v", err), http.StatusInternalServerError)
		return
	}

	ttl, err := client.Client.TTL(ctx, key).Result()
	if err != nil {
		http.Error(w, fmt.Sprintf("Failed to get key TTL: %v", err), http.StatusInternalServerError)
		return
	}

	keyInfo := RedisKeyInfo{
		Key:  key,
		Type: keyType,
		TTL:  int64(ttl.Seconds()),
	}

	// Get value
	val, err := client.Client.Get(ctx, key).Result()
	if err != nil {
		http.Error(w, fmt.Sprintf("Failed to get key value: %v", err), http.StatusInternalServerError)
		return
	}

	keyInfo.Value = val

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(keyInfo)
}

func deleteRedisKeyHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	key := vars["key"]

	client := session.Manager
	if client == nil {
		http.Error(w, "Redis not available", http.StatusInternalServerError)
		return
	}

	ctx := context.Background()
	result, err := client.Client.Del(ctx, key).Result()
	if err != nil {
		http.Error(w, fmt.Sprintf("Failed to delete key: %v", err), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"deleted": result > 0,
		"message": fmt.Sprintf("Deleted %d key(s)", result),
	})
}

func listRedisSessionsHandler(w http.ResponseWriter, r *http.Request) {
	client := session.Manager
	if client == nil {
		http.Error(w, "Redis not available", http.StatusInternalServerError)
		return
	}

	ctx := context.Background()
	sessionKeys, err := client.Client.Keys(ctx, "session:*").Result()
	if err != nil {
		http.Error(w, fmt.Sprintf("Failed to get session keys: %v", err), http.StatusInternalServerError)
		return
	}

	var sessions []SessionInfo
	for _, key := range sessionKeys {
		sessionID := strings.TrimPrefix(key, "session:")
		sessionData, err := session.Manager.GetSessionReadOnly(sessionID)
		if err == nil {
			sessions = append(sessions, SessionInfo{
				SessionID:   sessionID,
				Email:       sessionData.Email,
				AuthType:    sessionData.AuthType,
				Permissions: sessionData.Permissions,
				CreatedAt:   sessionData.CreatedAt,
				LastAccess:  sessionData.LastAccess,
				IPAddress:   sessionData.IPAddress,
			})
		}
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(sessions)
}

func deleteRedisSessionHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	sessionID := vars["sessionId"]

	err := session.Manager.DeleteSession(sessionID)
	if err != nil {
		http.Error(w, fmt.Sprintf("Failed to delete session: %v", err), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]string{
		"message": "Session deleted successfully",
	})
}

func flushRedisDataHandler(w http.ResponseWriter, r *http.Request) {
	client := session.Manager
	if client == nil {
		http.Error(w, "Redis not available", http.StatusInternalServerError)
		return
	}

	ctx := context.Background()
	err := client.Client.FlushAll(ctx).Err()
	if err != nil {
		http.Error(w, fmt.Sprintf("Failed to flush Redis: %v", err), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]string{
		"message": "All Redis data cleared successfully",
	})
}
