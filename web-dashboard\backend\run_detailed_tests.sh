
#!/bin/bash

echo "========================================"
echo "  WEB DASHBOARD - DETAILED TEST REPORT"
echo "========================================"
echo ""

echo "🔍 Test Environment Information:"
echo "  Go Version: $(go version)"
echo "  Test Time: $(date)"
echo "  Working Directory: $(pwd)"
echo ""

# Set environment variables
export TESTING=true
export PGHOST="${PGHOST:-localhost}"
export PGPORT="${PGPORT:-5432}"
export PGUSER="${PGUSER:-test}"
export PGPASSWORD="${PGPASSWORD:-test}"
export PGDATABASE="${PGDATABASE:-test}"
export REDIS_URL="${REDIS_URL:-redis://0.0.0.0:6379/1}"

echo "🧪 Running Go Tests..."
echo "========================================"

# Run tests with cleaner output, separate stderr from stdout
go test -v -race -count=1 -timeout=60s ./... > test_output.tmp 2> test_errors.tmp

# Combine outputs but keep structure
{
    echo "=== TEST RESULTS ==="
    cat test_output.tmp
    if [ -s test_errors.tmp ]; then
        echo ""
        echo "=== ERROR OUTPUT ==="
        cat test_errors.tmp
    fi
} > combined_output.tmp

echo ""
echo "📊 Test Summary:"
echo "========================================"

# Parse test results more accurately and ensure numeric values
TOTAL_TESTS=$(grep -c "^=== RUN" test_output.tmp 2>/dev/null || echo "0")
PASSED_TESTS=$(grep -c "^--- PASS:" test_output.tmp 2>/dev/null || echo "0")
FAILED_TESTS=$(grep -c "^--- FAIL:" test_output.tmp 2>/dev/null || echo "0")
SKIPPED_TESTS=$(grep -c "^--- SKIP:" test_output.tmp 2>/dev/null || echo "0")

# Check for package-level failures
PACKAGE_FAILURES=$(grep -c "^FAIL[[:space:]]" test_output.tmp 2>/dev/null || echo "0")
BUILD_FAILURES=$(grep -c "build failed" test_errors.tmp 2>/dev/null || echo "0")

# Ensure all variables are valid integers and handle empty values
TOTAL_TESTS=${TOTAL_TESTS:-0}; [[ ! "$TOTAL_TESTS" =~ ^[0-9]+$ ]] && TOTAL_TESTS=0
PASSED_TESTS=${PASSED_TESTS:-0}; [[ ! "$PASSED_TESTS" =~ ^[0-9]+$ ]] && PASSED_TESTS=0
FAILED_TESTS=${FAILED_TESTS:-0}; [[ ! "$FAILED_TESTS" =~ ^[0-9]+$ ]] && FAILED_TESTS=0
SKIPPED_TESTS=${SKIPPED_TESTS:-0}; [[ ! "$SKIPPED_TESTS" =~ ^[0-9]+$ ]] && SKIPPED_TESTS=0
PACKAGE_FAILURES=${PACKAGE_FAILURES:-0}; [[ ! "$PACKAGE_FAILURES" =~ ^[0-9]+$ ]] && PACKAGE_FAILURES=0
BUILD_FAILURES=${BUILD_FAILURES:-0}; [[ ! "$BUILD_FAILURES" =~ ^[0-9]+$ ]] && BUILD_FAILURES=0

# Calculate actual failures
ACTUAL_FAILURES=$((FAILED_TESTS + PACKAGE_FAILURES + BUILD_FAILURES))

echo "📈 Test Statistics:"
echo "  Total Tests Started: $TOTAL_TESTS"
echo "  ✅ Passed: $PASSED_TESTS"
echo "  ❌ Failed: $FAILED_TESTS"
echo "  ⏭️  Skipped: $SKIPPED_TESTS"

if [ "$PACKAGE_FAILURES" -gt 0 ]; then
    echo "  📦 Package Failures: $PACKAGE_FAILURES"
fi

if [ "$BUILD_FAILURES" -gt 0 ]; then
    echo "  🔨 Build Failures: $BUILD_FAILURES"
fi

echo ""
echo "📋 Test Results by Package:"
echo "========================================"

# Show results organized by package
grep "^=== RUN\|^--- PASS:\|^--- FAIL:\|^--- SKIP:\|^PASS\|^FAIL" test_output.tmp | \
while IFS= read -r line; do
    if [[ $line == PASS* ]] || [[ $line == FAIL* ]]; then
        echo "📦 Package: $line"
        echo "----------------------------------------"
    elif [[ $line == "=== RUN"* ]]; then
        test_name=$(echo "$line" | sed 's/=== RUN[[:space:]]*//')
        echo "  🏃 Running: $test_name"
    elif [[ $line == "--- PASS:"* ]]; then
        test_name=$(echo "$line" | sed 's/--- PASS:[[:space:]]*//' | sed 's/[[:space:]]*(.*//')
        echo "  ✅ $test_name"
    elif [[ $line == "--- FAIL:"* ]]; then
        test_name=$(echo "$line" | sed 's/--- FAIL:[[:space:]]*//' | sed 's/[[:space:]]*(.*//')
        echo "  ❌ $test_name"
    elif [[ $line == "--- SKIP:"* ]]; then
        test_name=$(echo "$line" | sed 's/--- SKIP:[[:space:]]*//' | sed 's/[[:space:]]*(.*//')
        echo "  ⏭️  $test_name"
    fi
done

if [ "$ACTUAL_FAILURES" -gt 0 ]; then
    echo ""
    echo "❌ Failure Details:"
    echo "========================================"
    
    # Show test failures with context
    grep -A 10 "^--- FAIL:" test_output.tmp | while IFS= read -r line; do
        if [[ $line == "--- FAIL:"* ]]; then
            echo "🔴 $line"
        elif [[ $line == "--" ]]; then
            echo "----------------------------------------"
        else
            echo "   $line"
        fi
    done
    
    # Show package failures
    if [ "$PACKAGE_FAILURES" -gt 0 ]; then
        echo ""
        echo "📦 Package Failure Details:"
        echo "========================================"
        grep -B 2 -A 5 "^FAIL[[:space:]]" test_output.tmp
    fi
    
    # Show build failures
    if [ "$BUILD_FAILURES" -gt 0 ]; then
        echo ""
        echo "🔨 Build Failure Details:"
        echo "========================================"
        cat test_errors.tmp
    fi
fi

echo ""
echo "📁 Output Files:"
echo "  • Combined output: combined_output.tmp"
echo "  • Test output: test_output.tmp" 
echo "  • Error output: test_errors.tmp"
echo "========================================"

# Cleanup intermediate files on success
if [ "$ACTUAL_FAILURES" -eq 0 ]; then
    rm -f test_output.tmp test_errors.tmp
fi

# Return appropriate exit code
exit $ACTUAL_FAILURES
