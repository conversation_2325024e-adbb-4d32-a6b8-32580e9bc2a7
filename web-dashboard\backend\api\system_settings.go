package api

import (
	"encoding/json"
	"fmt"
	"net/http"

	"strings"
	"web-dashboard/backend/email"
	"web-dashboard/backend/session"
	"web-dashboard/backend/system_settings"
	"web-dashboard/backend/users" // Import the users package

	"github.com/gorilla/mux"
)

type TestEmailRequest struct {
	To      string `json:"to"`
	Subject string `json:"subject"`
	Body    string `json:"body"`
}

type SystemSetting struct {
	Key       string      `json:"key"`
	Value     interface{} `json:"value"`
	Category  string      `json:"category"`
	ValueType string      `json:"value_type"` // e.g., "string", "integer", "boolean", "json"
}

type SystemSettingsRequest struct {
	Key       string      `json:"key"`
	Value     interface{} `json:"value"`
	Category  string      `json:"category"`
	ValueType string      `json:"value_type"`
}

type SystemSettingsResponse struct {
	Message string `json:"message"`
	Success bool   `json:"success"`
}

type SystemSettingsPermissionRequest struct {
	Action string `json:"action"`
}

type SystemSettingsPermissionResponse struct {
	HasPermission bool `json:"has_permission"`
}

type ActivateUserRequest struct {
	UserID int `json:"user_id"`
}

type AddDomainRequest struct {
	Domain string `json:"domain"`
}

type Domain struct {
	ID        int    `json:"id"`
	Domain    string `json:"domain"`
	CreatedAt string `json:"created_at"`
}

// AttachSystemSettingsRoutes attaches all system settings routes to the router
func AttachSystemSettingsRoutes(router *mux.Router) {
	router.HandleFunc("/system-settings/permissions/check", RequireSystemSettings(checkSystemSettingsPermissionHandler)).Methods("POST")
	router.HandleFunc("/system-settings/test-email", RequireSystemSettings(testEmailHandler)).Methods("POST")
	router.HandleFunc("/system-settings/users", RequireSystemSettings(getUsersHandler)).Methods("GET")
	router.HandleFunc("/system-settings/users/enable", RequireSystemSettings(enableUserHandler)).Methods("POST")
	router.HandleFunc("/system-settings/users/delete", RequireSystemSettings(deleteUserHandler)).Methods("POST")
	router.HandleFunc("/system-settings/users/disable", RequireSystemSettings(disableUserHandler)).Methods("POST")
	router.HandleFunc("/system-settings/initialize", RequireSystemSettings(initializeSystemSettingsHandler)).Methods("POST")

	// System settings management
	router.HandleFunc("/system-settings/settings", RequireSystemSettings(getAllSettingsHandler)).Methods("GET")
	router.HandleFunc("/system-settings/settings/{category}", RequireSystemSettings(getSettingsByCategoryHandler)).Methods("GET")
	router.HandleFunc("/system-settings/settings", RequireSystemSettings(createOrUpdateSettingHandler)).Methods("POST")
	router.HandleFunc("/system-settings/settings/{key}", RequireSystemSettings(getSettingHandler)).Methods("GET")
	router.HandleFunc("/system-settings/settings/{key}", RequireSystemSettings(updateSettingHandler)).Methods("PUT")
	router.HandleFunc("/system-settings/settings/{key}", RequireSystemSettings(deleteSettingHandler)).Methods("DELETE")

	router.HandleFunc("/system-settings/debug-levels", RequireSystemSettings(getDebugLevelsHandler)).Methods("GET")
}

func testEmailHandler(w http.ResponseWriter, r *http.Request) {

	var req TestEmailRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	if req.To == "" || req.Subject == "" {
		http.Error(w, "To and Subject are required", http.StatusBadRequest)
		return
	}

	// Create Gmail service and send test email
	gmailService, err := email.NewGmailService()
	if err != nil {
		response := SystemSettingsResponse{
			Message: "Failed to create email service: " + err.Error(),
			Success: false,
		}
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusInternalServerError)
		json.NewEncoder(w).Encode(response)
		return
	}

	// Create email message
	msg := email.EmailMessage{
		To:      []string{req.To},
		Subject: req.Subject,
		Body:    req.Body,
		IsHTML:  false,
	}

	err = gmailService.SendEmail(msg)
	if err != nil {
		response := SystemSettingsResponse{
			Message: "Failed to send test email: " + err.Error(),
			Success: false,
		}
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusInternalServerError)
		json.NewEncoder(w).Encode(response)
		return
	}

	response := SystemSettingsResponse{
		Message: "Test email sent successfully",
		Success: true,
	}
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

func getUsersHandler(w http.ResponseWriter, r *http.Request) {
	userList, err := users.GetAllUsers()
	if err != nil {
		http.Error(w, "Failed to get users", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(userList)
}

func checkSystemSettingsPermissionHandler(w http.ResponseWriter, r *http.Request) {
	// Get session data from context
	sessionData := r.Context().Value("session").(*session.SessionData)
	if sessionData == nil {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	var req SystemSettingsPermissionRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	// Check permissions from Redis session
	hasPermission := false
	for _, perm := range sessionData.Permissions {
		// Check for wildcard or specific system settings permissions
		if perm == "*" || strings.Contains(perm, "System Settings") {
			hasPermission = true
			break
		}
		// For specific action checks, you could also check for more granular permissions
		// if req.Action == "manage_email" && strings.Contains(perm, "System Settings - Manage Email") {
		//     hasPermission = true
		//     break
		// }
	}

	response := SystemSettingsPermissionResponse{
		HasPermission: hasPermission,
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// enableUserHandler enables a user.
func enableUserHandler(w http.ResponseWriter, r *http.Request) {
	var req ActivateUserRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	err := users.EnableUser(req.UserID)
	if err != nil {
		http.Error(w, "Failed to enable user", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]bool{"success": true})
}

// disableUserHandler disables a user.
func disableUserHandler(w http.ResponseWriter, r *http.Request) {
	var req ActivateUserRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	err := users.DisableUser(req.UserID)
	if err != nil {
		http.Error(w, "Failed to disable user", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]bool{"success": true})
}

// deleteUserHandler deletes a user.
func deleteUserHandler(w http.ResponseWriter, r *http.Request) {
	var req ActivateUserRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	err := users.DeleteUser(req.UserID)
	if err != nil {
		http.Error(w, "Failed to delete user", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]bool{"success": true})
}

// Function to get all system settings
func getAllSettingsHandler(w http.ResponseWriter, r *http.Request) {
	settings, err := system_settings.GetAllSystemSettings()
	if err != nil {
		http.Error(w, "Failed to get all settings: "+err.Error(), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(settings)
}

// Function to get settings by category
func getSettingsByCategoryHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	category := vars["category"]

	settings, err := system_settings.GetSystemSettingsByCategory(category)
	if err != nil {
		http.Error(w, "Failed to get settings by category: "+err.Error(), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(settings)
}

// Function to get a single setting by key
func getSettingHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	key := vars["key"]

	setting, err := system_settings.GetSystemSetting(key)
	if err != nil {
		http.Error(w, "Failed to get setting: "+err.Error(), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(setting)
}

// Function to create or update a setting
func createOrUpdateSettingHandler(w http.ResponseWriter, r *http.Request) {
	var req SystemSettingsRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	err := system_settings.CreateOrUpdateSystemSetting(req.Key, req.Value, req.Category, req.ValueType)
	if err != nil {
		http.Error(w, "Failed to create/update setting: "+err.Error(), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]bool{"success": true})
}

// Function to update an existing setting
func updateSettingHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	key := vars["key"]

	var req SystemSettingsRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	err := system_settings.UpdateSystemSetting(key, req.Value, req.Category, req.ValueType)
	if err != nil {
		http.Error(w, "Failed to update setting: "+err.Error(), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]bool{"success": true})
}

// Function to delete a setting
func deleteSettingHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	key := vars["key"]

	err := system_settings.DeleteSystemSetting(key)
	if err != nil {
		http.Error(w, "Failed to delete setting: "+err.Error(), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]bool{"success": true})
}

func initializeSystemSettingsHandler(w http.ResponseWriter, r *http.Request) {
	// Get all existing system settings to check what's already initialized
	existingSettings, err := system_settings.GetAllSystemSettings()
	if err != nil {
		http.Error(w, "Failed to get existing settings: "+err.Error(), http.StatusInternalServerError)
		return
	}

	// Create a map of existing setting keys for quick lookup
	existingKeys := make(map[string]bool)
	for _, setting := range existingSettings {
		existingKeys[setting.Key] = true
	}

	// Get the expected default settings from the database schema
	// This reads the schema dynamically rather than hard-coding values
	defaultSettingsFromSchema := getDefaultSystemSettingsFromSchema()

	initializationResults := make(map[string]string)
	successCount := 0

	// Initialize any missing default settings
	for _, defaultSetting := range defaultSettingsFromSchema {
		if !existingKeys[defaultSetting.Key] {
			err = system_settings.CreateOrUpdateSystemSetting(
				defaultSetting.Key,
				defaultSetting.Value,
				defaultSetting.Category,
				defaultSetting.ValueType,
			)
			if err != nil {
				initializationResults[defaultSetting.Key] = fmt.Sprintf("Failed: %v", err)
			} else {
				initializationResults[defaultSetting.Key] = "Created"
				successCount++
			}
		} else {
			initializationResults[defaultSetting.Key] = "Already exists"
		}
	}

	response := map[string]interface{}{
		"success": true,
		"message": fmt.Sprintf("Initialization complete. %d settings processed, %d created", len(initializationResults), successCount),
		"results": initializationResults,
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// getDefaultSystemSettingsFromSchema returns the default system settings that should exist
// This could be extended to read from schema.sql file or other configuration sources
func getDefaultSystemSettingsFromSchema() []struct {
	Key       string
	Value     interface{}
	Category  string
	ValueType string
} {
	return []struct {
		Key       string
		Value     interface{}
		Category  string
		ValueType string
	}{
		{"app_name", "Web Dashboard", "general", "string"},
		{"app_version", "1.0.0", "general", "string"},
		{"maintenance_mode", false, "general", "boolean"},
		{"max_login_attempts", 5, "security", "integer"},
		{"session_timeout_hours", 24, "security", "integer"},
		{"email_notifications_enabled", true, "email", "boolean"},
		{"recaptcha_enabled", false, "security", "boolean"},
		{"site_url", "https://abf82530-f459-4235-b523-4961d3b9ea50-00-3p2c1vyhrggly.kirk.replit.dev:5000", "general", "string"},
		{"session_timeout", 86400, "security", "integer"},
		{"password_min_length", 8, "security", "integer"},
		{"password_require_special", true, "security", "boolean"},
		{"email_from_address", "", "email", "string"},
		{"email_from_name", "Web Dashboard", "email", "string"},
		{"api_rate_limit", 100, "api", "integer"},
		{"debug_level", "INFO", "general", "string"},
	}
}

func getDebugLevelsHandler(w http.ResponseWriter, r *http.Request) {
	debugLevels := []map[string]interface{}{
		{"value": "ERROR", "label": "ERROR - Errors only"},
		{"value": "WARN", "label": "WARN - Warnings and errors"},
		{"value": "INFO", "label": "INFO - General information (default)"},
		{"value": "DEBUG", "label": "DEBUG - Debug information"},
		{"value": "TRACE", "label": "TRACE - Most verbose (all messages)"},
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(debugLevels)
}
