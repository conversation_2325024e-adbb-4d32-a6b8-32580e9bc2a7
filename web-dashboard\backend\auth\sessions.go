package auth

import (
	"crypto/rand"
	"encoding/hex"

	"web-dashboard/backend/database"
)

// Session struct to hold session data
type Session struct {
	UserID       int
	SessionToken string
}

// generateSessionID creates a secure random session ID
func generateSessionID() (string, error) {
	bytes := make([]byte, 32)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

// CreateSession creates a new session in the database
func CreateSession(userID int, sessionToken string) error {
	_, err := database.DB.Exec("INSERT INTO sessions (user_id, session_token, created_at) VALUES ($1, $2, CURRENT_TIMESTAMP)", userID, sessionToken)
	return err
}

// InvalidateSession removes a specific session from the database
func InvalidateSession(sessionToken string) error {
	_, err := database.DB.Exec("DELETE FROM sessions WHERE session_token = $1", sessionToken)
	return err
}

// InvalidateAllUserSessions removes all sessions for a specific user
func InvalidateAllUserSessions(userID int) error {
	_, err := database.DB.Exec("DELETE FROM sessions WHERE user_id = $1", userID)
	return err
}

// CleanupExpiredSessions removes sessions older than the specified duration
func CleanupExpiredSessions(maxAge string) error {
	query := "DELETE FROM sessions WHERE created_at < NOW() - INTERVAL '" + maxAge + "'"
	_, err := database.DB.Exec(query)
	return err
}

// GetSession retrieves a session from the database by session token
func GetSession(sessionToken string) (*Session, error) {
	session := &Session{}
	err := database.DB.QueryRow("SELECT user_id, session_token FROM sessions WHERE session_token = $1", sessionToken).Scan(&session.UserID, &session.SessionToken)
	if err != nil {
		return nil, err
	}
	return session, nil
}