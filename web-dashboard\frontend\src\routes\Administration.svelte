
<script>
	import { onMount } from 'svelte';

	export let userInfo = null;

	let loading = true;
	let error = null;
	let hasPermission = false;
	let activeTab = 'users'; // Default to users tab

	// Users management
	let users = [];
	let loadingUsers = false;
	let showCreateUserForm = false;
	let newUserEmail = '';
	let newUserPassword = '';
	let newUserConfirmPassword = '';
	let createUserLoading = false;
	let createUserMessage = '';
	let createUserMessageType = '';

	// Groups management
	let groups = [];
	let loadingGroups = false;
	let newGroupName = '';
	let newGroupDescription = '';
	let groupMessage = '';
	let groupMessageType = '';
	let selectedGroup = null;
	let groupMembers = [];
	let groupPermissions = [];
	let drawerOpen = false;
	let activeDrawerTab = 'members';

	// Permissions management
	let permissions = [];
	let loadingPermissions = false;
	let availableUsers = [];
	let availableGroups = [];

	// User permission assignment
	let selectedUser = null;
	let userPermissions = [];
	let userGroups = [];
	let userDrawerOpen = false;
	let activeUserDrawerTab = 'permissions';

	// Reactive statement that runs when userInfo prop changes
	$: {
		if (userInfo) {
			initializeComponent();
		} else {
			loading = false;
			error = 'User information not available';
		}
	}

	async function initializeComponent() {
		try {
			// Check if user has administration permissions
			const hasUserMgmt = await checkPermission('user_management', 'view');
			const hasGroupMgmt = await checkPermission('group_management', 'view');
			const hasPermMgmt = await checkPermission('permission_management', 'view');

			hasPermission = hasUserMgmt || hasGroupMgmt || hasPermMgmt;

			if (!hasPermission) {
				error = 'Access denied - insufficient permissions for administration';
				return;
			}

			// Load all data
			await Promise.all([
				loadUsers(),
				loadGroups(),
				loadPermissions()
			]);
		} catch (err) {
			error = 'Failed to initialize administration interface';
			console.error('Error initializing administration interface:', err);
		} finally {
			loading = false;
		}
	}

	async function checkPermission(resource, action) {
		try {
			const response = await fetch('/api/system-settings/permissions/check', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					resource: resource,
					action: action
				})
			});

			if (response.ok) {
				const result = await response.json();
				return result.has_permission;
			}
			return false;
		} catch (error) {
			console.error('Error checking permission:', error);
			return false;
		}
	}

	async function loadUsers() {
		loadingUsers = true;
		try {
			const response = await fetch('/api/system-settings/users');
			if (response.ok) {
				users = await response.json();
				availableUsers = users;
			}
		} catch (error) {
			console.error('Failed to load users:', error);
		} finally {
			loadingUsers = false;
		}
	}

	async function loadGroups() {
		loadingGroups = true;
		try {
			const response = await fetch('/api/security-groups');
			if (response.ok) {
				groups = await response.json();
				availableGroups = groups;
			}
		} catch (error) {
			console.error('Failed to load groups:', error);
		} finally {
			loadingGroups = false;
		}
	}

	async function loadPermissions() {
		loadingPermissions = true;
		try {
			const response = await fetch('/api/permissions');
			if (response.ok) {
				permissions = await response.json();
			}
		} catch (error) {
			console.error('Failed to load permissions:', error);
		} finally {
			loadingPermissions = false;
		}
	}

	async function createUser() {
		if (!newUserEmail || !newUserPassword || !newUserConfirmPassword) {
			createUserMessage = 'All fields are required';
			createUserMessageType = 'error';
			return;
		}

		if (newUserPassword !== newUserConfirmPassword) {
			createUserMessage = 'Passwords do not match';
			createUserMessageType = 'error';
			return;
		}

		createUserLoading = true;
		createUserMessage = '';

		try {
			const response = await fetch('/api/users', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					email: newUserEmail,
					password: newUserPassword
				})
			});

			if (response.ok) {
				createUserMessage = 'User created successfully';
				createUserMessageType = 'success';
				newUserEmail = '';
				newUserPassword = '';
				newUserConfirmPassword = '';
				showCreateUserForm = false;
				await loadUsers();
			} else {
				const errorText = await response.text();
				createUserMessage = errorText || 'Failed to create user';
				createUserMessageType = 'error';
			}
		} catch (error) {
			createUserMessage = 'Error creating user';
			createUserMessageType = 'error';
		} finally {
			createUserLoading = false;
		}
	}

	async function enableUser(userId) {
		try {
			const response = await fetch('/api/system-settings/users/enable', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					user_id: userId
				})
			});

			if (response.ok) {
				await loadUsers();
			}
		} catch (error) {
			console.error('Failed to enable user:', error);
		}
	}

	async function disableUser(userId) {
		try {
			const response = await fetch('/api/system-settings/users/disable', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					user_id: userId
				})
			});

			if (response.ok) {
				await loadUsers();
			}
		} catch (error) {
			console.error('Failed to disable user:', error);
		}
	}

	async function createGroup() {
		if (!newGroupName) return;

		try {
			const response = await fetch('/api/security-groups', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					name: newGroupName,
					description: newGroupDescription
				})
			});

			if (response.ok) {
				groupMessage = 'Group created successfully';
				groupMessageType = 'success';
				newGroupName = '';
				newGroupDescription = '';
				await loadGroups();
			} else {
				groupMessage = 'Failed to create group';
				groupMessageType = 'error';
			}
		} catch (error) {
			groupMessage = 'Error creating group';
			groupMessageType = 'error';
		}
	}

	async function deleteGroup(groupId) {
		if (!confirm('Are you sure you want to delete this group?')) return;

		try {
			const response = await fetch(`/api/security-groups/${groupId}`, {
				method: 'DELETE'
			});

			if (response.ok) {
				await loadGroups();
				if (selectedGroup && selectedGroup.id === groupId) {
					closeDrawer();
				}
			}
		} catch (error) {
			console.error('Error deleting group:', error);
		}
	}

	async function selectGroup(group) {
		selectedGroup = group;
		drawerOpen = true;
		await loadGroupMembers(group.id);
		await loadGroupPermissions(group.id);
	}

	async function selectUser(user) {
		selectedUser = user;
		userDrawerOpen = true;
		await loadUserPermissions(user.id);
		await loadUserGroups(user.id);
	}

	function closeDrawer() {
		drawerOpen = false;
		selectedGroup = null;
		groupMembers = [];
		groupPermissions = [];
	}

	function closeUserDrawer() {
		userDrawerOpen = false;
		selectedUser = null;
		userPermissions = [];
		userGroups = [];
	}

	async function loadGroupMembers(groupId) {
		try {
			const response = await fetch(`/api/security-groups/${groupId}/members`);
			if (response.ok) {
				const memberIds = await response.json();
				groupMembers = [];
				for (const userId of memberIds) {
					const user = users.find(u => u.id === userId);
					if (user) {
						groupMembers.push(user);
					}
				}
			}
		} catch (error) {
			console.error('Error loading group members:', error);
		}
	}

	async function loadGroupPermissions(groupId) {
		try {
			const response = await fetch(`/api/security-groups/${groupId}/permissions`);
			if (response.ok) {
				groupPermissions = await response.json() || [];
			}
		} catch (error) {
			console.error('Error loading group permissions:', error);
			groupPermissions = [];
		}
	}

	async function loadUserPermissions(userId) {
		try {
			const response = await fetch(`/api/users/${userId}/permissions`);
			if (response.ok) {
				userPermissions = await response.json() || [];
			}
		} catch (error) {
			console.error('Error loading user permissions:', error);
			userPermissions = [];
		}
	}

	async function loadUserGroups(userId) {
		try {
			const response = await fetch(`/api/users/${userId}/groups`);
			if (response.ok) {
				const groupIds = await response.json() || [];
				userGroups = [];
				for (const groupId of groupIds) {
					const group = groups.find(g => g.id === groupId);
					if (group) {
						userGroups.push(group);
					}
				}
			}
		} catch (error) {
			console.error('Error loading user groups:', error);
			userGroups = [];
		}
	}

	async function removeUserFromGroup(userId, groupId) {
		try {
			const response = await fetch(`/api/security-groups/${groupId}/members/${userId}`, {
				method: 'DELETE'
			});

			if (response.ok) {
				await loadGroupMembers(groupId);
			}
		} catch (error) {
			console.error('Error removing user from group:', error);
		}
	}

	async function removePermissionFromGroup(permissionId, groupId) {
		try {
			const response = await fetch(`/api/security-groups/${groupId}/permissions/${permissionId}`, {
				method: 'DELETE'
			});

			if (response.ok) {
				await loadGroupPermissions(groupId);
			}
		} catch (error) {
			console.error('Error removing permission from group:', error);
		}
	}

	function formatDate(dateString) {
		return new Date(dateString).toLocaleDateString();
	}

	function toggleCreateUserForm() {
		showCreateUserForm = !showCreateUserForm;
		if (!showCreateUserForm) {
			newUserEmail = '';
			newUserPassword = '';
			newUserConfirmPassword = '';
			createUserMessage = '';
		}
	}
</script>

<div class="administration-container">
	<h1>Administration</h1>
	<p class="subtitle">Manage users, groups, and permissions</p>

	<div class="tab-navigation">
		<button class="tab-button" class:active={activeTab === 'users'} on:click={() => activeTab = 'users'}>
			Users
		</button>
		<button class="tab-button" class:active={activeTab === 'groups'} on:click={() => activeTab = 'groups'}>
			Groups
		</button>
		<button class="tab-button" class:active={activeTab === 'permissions'} on:click={() => activeTab = 'permissions'}>
			Permissions
		</button>
	</div>

	{#if loading}
		<div class="loading">Loading administration interface...</div>
	{:else if error}
		<div class="error">
			<p>{error}</p>
			<button on:click={() => window.location.reload()}>Retry</button>
		</div>
	{:else if !hasPermission}
		<div class="access-denied">
			<h2>Access Denied</h2>
			<p>You don't have permission to access the Administration interface.</p>
			<p>Please contact your administrator if you believe this is an error.</p>
			<button class="secondary-button" on:click={() => window.history.back()}>
				← Back
			</button>
		</div>
	{:else}
		{#if activeTab === 'users'}
		<div class="admin-card">
			<div class="admin-section">
				<div class="section-header">
					<h2>User Management</h2>
					<button class="primary-button" on:click={toggleCreateUserForm}>
						{showCreateUserForm ? 'Cancel' : 'Create New User'}
					</button>
				</div>

				{#if showCreateUserForm}
					<div class="create-form">
						<h3>Create New User</h3>
						<form on:submit|preventDefault={createUser}>
							<div class="form-group">
								<label for="new-user-email">Email:</label>
								<input
									type="email"
									id="new-user-email"
									bind:value={newUserEmail}
									placeholder="Enter user email"
									required
									disabled={createUserLoading}
								/>
							</div>
							<div class="form-group">
								<label for="new-user-password">Password:</label>
								<input
									type="password"
									id="new-user-password"
									bind:value={newUserPassword}
									placeholder="Enter password"
									required
									disabled={createUserLoading}
								/>
							</div>
							<div class="form-group">
								<label for="new-user-confirm-password">Confirm Password:</label>
								<input
									type="password"
									id="new-user-confirm-password"
									bind:value={newUserConfirmPassword}
									placeholder="Confirm password"
									required
									disabled={createUserLoading}
								/>
							</div>
							<div class="form-actions">
								<button type="submit" class="primary-button" disabled={createUserLoading}>
									{createUserLoading ? 'Creating...' : 'Create User'}
								</button>
								<button type="button" class="secondary-button" on:click={toggleCreateUserForm} disabled={createUserLoading}>
									Cancel
								</button>
							</div>
						</form>
						{#if createUserMessage}
							<div class="message" class:error={createUserMessageType === 'error'} class:success={createUserMessageType === 'success'}>
								{createUserMessage}
							</div>
						{/if}
					</div>
				{/if}

				{#if loadingUsers}
					<div class="loading">Loading users...</div>
				{:else}
					<div class="data-table">
						<table>
							<thead>
								<tr>
									<th>Email</th>
									<th>Status</th>
									<th>Created</th>
									<th>Last Login</th>
									<th>Actions</th>
								</tr>
							</thead>
							<tbody>
								{#each users as user}
									<tr>
										<td class="user-email" on:click={() => selectUser(user)}>{user.email}</td>
										<td>
											<span class="status" class:enabled={user.enabled} class:disabled={!user.enabled}>
												{user.enabled ? 'Enabled' : 'Disabled'}
											</span>
										</td>
										<td>{formatDate(user.created_at)}</td>
										<td>{user.last_login_at ? formatDate(user.last_login_at) : 'Never'}</td>
										<td>
											{#if user.enabled}
												<button class="action-btn disable" on:click={() => disableUser(user.id)}>
													Disable
												</button>
											{:else}
												<button class="action-btn enable" on:click={() => enableUser(user.id)}>
													Enable
												</button>
											{/if}
											<button class="action-btn view" on:click={() => selectUser(user)}>
												Manage
											</button>
										</td>
									</tr>
								{/each}
							</tbody>
						</table>
					</div>
				{/if}
			</div>
		</div>

		{:else if activeTab === 'groups'}
		<div class="admin-card">
			<div class="admin-section">
				<div class="section-header">
					<h2>Group Management</h2>
				</div>

				<div class="create-form">
					<h3>Create New Group</h3>
					<div class="form-group">
						<label for="group-name">Group Name:</label>
						<input
							type="text"
							id="group-name"
							bind:value={newGroupName}
							placeholder="Enter group name"
						/>
					</div>
					<div class="form-group">
						<label for="group-description">Description:</label>
						<input
							type="text"
							id="group-description"
							bind:value={newGroupDescription}
							placeholder="Enter group description"
						/>
					</div>
					<button type="button" class="primary-button" on:click={createGroup}>
						Create Group
					</button>
					{#if groupMessage}
						<div class="message" class:error={groupMessageType === 'error'} class:success={groupMessageType === 'success'}>
							{groupMessage}
						</div>
					{/if}
				</div>

				{#if loadingGroups}
					<div class="loading">Loading groups...</div>
				{:else if groups.length > 0}
					<div class="groups-grid">
						{#each groups as group}
							<div class="group-card" class:selected={selectedGroup && selectedGroup.id === group.id}>
								<div class="group-info" role="button" tabindex="0" on:click={() => selectGroup(group)} on:keydown={(e) => e.key === 'Enter' && selectGroup(group)}>
									<h4>{group.name}</h4>
									<p>{group.description}</p>
									<small>Created: {formatDate(group.created_at)}</small>
								</div>
								<div class="group-actions">
									<button class="action-btn delete" on:click={() => deleteGroup(group.id)}>
										Delete
									</button>
								</div>
							</div>
						{/each}
					</div>
				{:else}
					<p>No groups found.</p>
				{/if}
			</div>
		</div>

		{:else if activeTab === 'permissions'}
		<div class="admin-card">
			<div class="admin-section">
				<div class="section-header">
					<h2>Permission Management</h2>
				</div>

				{#if loadingPermissions}
					<div class="loading">Loading permissions...</div>
				{:else}
					<div class="data-table">
						<table>
							<thead>
								<tr>
									<th>Name</th>
									<th>Description</th>
									<th>Resource</th>
									<th>Action</th>
									<th>Created</th>
								</tr>
							</thead>
							<tbody>
								{#each permissions as permission}
									<tr>
										<td class="permission-name">{permission.name}</td>
										<td class="permission-description">{permission.description}</td>
										<td><code class="resource">{permission.resource}</code></td>
										<td><code class="action">{permission.action}</code></td>
										<td>{formatDate(permission.created_at)}</td>
									</tr>
								{/each}
							</tbody>
						</table>
					</div>
				{/if}
			</div>
		</div>
		{/if}

		<!-- Group Details Drawer -->
		{#if selectedGroup}
		<div class="drawer-overlay" class:open={drawerOpen} role="button" tabindex="0" on:click={closeDrawer} on:keydown={(e) => e.key === 'Enter' && closeDrawer()}></div>
		<div class="group-drawer" class:open={drawerOpen}>
			<div class="drawer-header">
				<h3>{selectedGroup.name}</h3>
				<button class="close-btn" on:click={closeDrawer}>×</button>
			</div>

			<div class="drawer-tabs">
				<button 
					class="tab-btn" 
					class:active={activeDrawerTab === 'members'} 
					on:click={() => activeDrawerTab = 'members'}
				>
					Members ({groupMembers.length})
				</button>
				<button 
					class="tab-btn" 
					class:active={activeDrawerTab === 'permissions'} 
					on:click={() => activeDrawerTab = 'permissions'}
				>
					Permissions ({groupPermissions.length})
				</button>
			</div>

			<div class="drawer-content">
				{#if activeDrawerTab === 'members'}
					<div class="tab-panel">
						<h4>Group Members</h4>
						{#if groupMembers.length > 0}
							<div class="members-list">
								{#each groupMembers as member}
									<div class="member-item">
										<div class="member-info">
											<span class="member-email">{member.email}</span>
											<small>ID: {member.id}</small>
										</div>
										<button class="action-btn remove" on:click={() => removeUserFromGroup(member.id, selectedGroup.id)}>
											Remove
										</button>
									</div>
								{/each}
							</div>
						{:else}
							<div class="empty-state">
								<p>No members in this group.</p>
								<small>Add users to this group to grant them the group's permissions.</small>
							</div>
						{/if}
					</div>
				{:else if activeDrawerTab === 'permissions'}
					<div class="tab-panel">
						<h4>Group Permissions</h4>
						{#if groupPermissions && groupPermissions.length > 0}
							<div class="permissions-list">
								{#each groupPermissions as permission}
									<div class="permission-item">
										<div class="permission-info">
											<div class="permission-name">{permission.name}</div>
											<div class="permission-description">{permission.description}</div>
											<div class="permission-details">
												<span class="resource">{permission.resource}</span>
												<span class="action">{permission.action}</span>
											</div>
										</div>
										<button class="action-btn remove" on:click={() => removePermissionFromGroup(permission.id, selectedGroup.id)}>
											Remove
										</button>
									</div>
								{/each}
							</div>
						{:else}
							<div class="empty-state">
								<p>No permissions assigned to this group.</p>
								<small>Assign permissions to control what group members can access.</small>
							</div>
						{/if}
					</div>
				{/if}
			</div>
		</div>
		{/if}

		<!-- User Details Drawer -->
		{#if selectedUser}
		<div class="drawer-overlay" class:open={userDrawerOpen} role="button" tabindex="0" on:click={closeUserDrawer} on:keydown={(e) => e.key === 'Enter' && closeUserDrawer()}></div>
		<div class="user-drawer" class:open={userDrawerOpen}>
			<div class="drawer-header">
				<h3>{selectedUser.email}</h3>
				<button class="close-btn" on:click={closeUserDrawer}>×</button>
			</div>

			<div class="drawer-tabs">
				<button 
					class="tab-btn" 
					class:active={activeUserDrawerTab === 'permissions'} 
					on:click={() => activeUserDrawerTab = 'permissions'}
				>
					Permissions ({userPermissions.length})
				</button>
				<button 
					class="tab-btn" 
					class:active={activeUserDrawerTab === 'groups'} 
					on:click={() => activeUserDrawerTab = 'groups'}
				>
					Groups ({userGroups.length})
				</button>
			</div>

			<div class="drawer-content">
				{#if activeUserDrawerTab === 'permissions'}
					<div class="tab-panel">
						<h4>User Permissions</h4>
						{#if userPermissions && userPermissions.length > 0}
							<div class="permissions-list">
								{#each userPermissions as permission}
									<div class="permission-item">
										<div class="permission-info">
											<div class="permission-name">{permission.name}</div>
											<div class="permission-description">{permission.description}</div>
											<div class="permission-details">
												<span class="resource">{permission.resource}</span>
												<span class="action">{permission.action}</span>
											</div>
										</div>
									</div>
								{/each}
							</div>
						{:else}
							<div class="empty-state">
								<p>No direct permissions assigned to this user.</p>
								<small>User permissions are typically inherited through group membership.</small>
							</div>
						{/if}
					</div>
				{:else if activeUserDrawerTab === 'groups'}
					<div class="tab-panel">
						<h4>User Groups</h4>
						{#if userGroups && userGroups.length > 0}
							<div class="groups-list">
								{#each userGroups as group}
									<div class="group-item">
										<div class="group-info">
											<div class="group-name">{group.name}</div>
											<div class="group-description">{group.description}</div>
										</div>
									</div>
								{/each}
							</div>
						{:else}
							<div class="empty-state">
								<p>User is not a member of any groups.</p>
								<small>Add user to groups to grant permissions.</small>
							</div>
						{/if}
					</div>
				{/if}
			</div>
		</div>
		{/if}
	{/if}
</div>

<style>
	.administration-container {
		max-width: 1200px;
		margin: 2rem auto;
		padding: 0 1rem;
	}

	h1 {
		color: #333;
		margin-bottom: 0.5rem;
		text-align: center;
	}

	.subtitle {
		text-align: center;
		color: #666;
		margin-bottom: 2rem;
		font-size: 1.1rem;
	}

	.tab-navigation {
		display: flex;
		margin-bottom: 2rem;
		border-bottom: 2px solid #ddd;
		justify-content: center;
	}

	.tab-button {
		padding: 1rem 2rem;
		border: none;
		background-color: transparent;
		cursor: pointer;
		font-size: 16px;
		color: #666;
		border-bottom: 2px solid transparent;
		transition: border-color 0.3s, color 0.3s;
		margin: 0 0.5rem;
	}

	.tab-button:hover {
		color: #333;
	}

	.tab-button.active {
		color: #007bff;
		border-color: #007bff;
		font-weight: bold;
	}

	.loading {
		text-align: center;
		padding: 2rem;
		color: #666;
	}

	.error {
		text-align: center;
		padding: 2rem;
		color: #dc3545;
	}

	.error button {
		margin-top: 1rem;
		padding: 0.5rem 1rem;
		background-color: #007bff;
		color: white;
		border: none;
		border-radius: 4px;
		cursor: pointer;
	}

	.access-denied {
		text-align: center;
		padding: 3rem;
		background: white;
		border-radius: 8px;
		box-shadow: 0 2px 10px rgba(0,0,0,0.1);
	}

	.access-denied h2 {
		color: #dc3545;
		margin-bottom: 1rem;
	}

	.access-denied p {
		color: #666;
		margin-bottom: 1rem;
	}

	.admin-card {
		background: white;
		border-radius: 8px;
		box-shadow: 0 2px 10px rgba(0,0,0,0.1);
		overflow: hidden;
	}

	.admin-section {
		padding: 2rem;
	}

	.section-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 2rem;
	}

	.section-header h2 {
		margin: 0;
		color: #333;
		font-size: 1.5rem;
	}

	.create-form {
		background-color: #f8f9fa;
		padding: 1.5rem;
		border-radius: 6px;
		border: 1px solid #e9ecef;
		margin-bottom: 2rem;
	}

	.create-form h3 {
		margin: 0 0 1rem 0;
		color: #333;
		font-size: 1.2rem;
	}

	.form-group {
		margin-bottom: 1rem;
	}

	.form-group label {
		display: block;
		margin-bottom: 0.5rem;
		font-weight: 600;
		color: #555;
	}

	.form-group input {
		width: 100%;
		max-width: 400px;
		padding: 0.75rem;
		border: 1px solid #ddd;
		border-radius: 4px;
		font-size: 16px;
		box-sizing: border-box;
	}

	.form-group input:focus {
		outline: none;
		border-color: #007bff;
		box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
	}

	.form-actions {
		display: flex;
		gap: 1rem;
		margin-top: 1rem;
	}

	.primary-button {
		padding: 0.75rem 1.5rem;
		background-color: #007bff;
		color: white;
		border: none;
		border-radius: 4px;
		cursor: pointer;
		font-size: 16px;
	}

	.primary-button:hover:not(:disabled) {
		background-color: #0056b3;
	}

	.primary-button:disabled {
		background-color: #6c757d;
		cursor: not-allowed;
	}

	.secondary-button {
		padding: 0.75rem 1.5rem;
		background-color: #6c757d;
		color: white;
		border: none;
		border-radius: 4px;
		cursor: pointer;
	}

	.secondary-button:hover {
		background-color: #5a6268;
	}

	.message {
		padding: 0.75rem;
		border-radius: 4px;
		margin-top: 1rem;
	}

	.message.success {
		background-color: #d4edda;
		color: #155724;
		border: 1px solid #c3e6cb;
	}

	.message.error {
		background-color: #f8d7da;
		color: #721c24;
		border: 1px solid #f5c6cb;
	}

	.data-table {
		overflow-x: auto;
		margin-top: 1rem;
	}

	table {
		width: 100%;
		border-collapse: collapse;
		background: white;
	}

	th, td {
		padding: 0.75rem;
		text-align: left;
		border-bottom: 1px solid #ddd;
	}

	th {
		background-color: #f8f9fa;
		font-weight: 600;
		color: #555;
	}

	.user-email {
		cursor: pointer;
		color: #007bff;
		font-weight: 500;
	}

	.user-email:hover {
		text-decoration: underline;
	}

	.status {
		padding: 0.25rem 0.5rem;
		border-radius: 4px;
		font-size: 0.875rem;
		font-weight: 500;
	}

	.status.enabled {
		background-color: #d4edda;
		color: #155724;
	}

	.status.disabled {
		background-color: #f8d7da;
		color: #721c24;
	}

	.action-btn {
		padding: 0.375rem 0.75rem;
		border: none;
		border-radius: 4px;
		cursor: pointer;
		font-size: 0.875rem;
		margin-right: 0.5rem;
	}

	.action-btn.view {
		background-color: #17a2b8;
		color: white;
	}

	.action-btn.view:hover {
		background-color: #138496;
	}

	.action-btn.enable {
		background-color: #28a745;
		color: white;
	}

	.action-btn.enable:hover {
		background-color: #218838;
	}

	.action-btn.disable {
		background-color: #6c757d;
		color: white;
	}

	.action-btn.disable:hover {
		background-color: #5a6268;
	}

	.action-btn.delete {
		background-color: #dc3545;
		color: white;
	}

	.action-btn.delete:hover {
		background-color: #c82333;
	}

	.action-btn.remove {
		background-color: #dc3545;
		color: white;
		font-size: 0.75rem;
		padding: 0.25rem 0.5rem;
	}

	.action-btn.remove:hover {
		background-color: #c82333;
	}

	.groups-grid {
		display: grid;
		gap: 1rem;
		margin-top: 1rem;
	}

	.group-card {
		border: 1px solid #e9ecef;
		border-radius: 6px;
		padding: 1rem;
		background-color: white;
		transition: border-color 0.2s, box-shadow 0.2s;
	}

	.group-card:hover {
		border-color: #007bff;
		box-shadow: 0 2px 8px rgba(0,123,255,0.15);
	}

	.group-card.selected {
		border-color: #007bff;
		box-shadow: 0 2px 8px rgba(0,123,255,0.25);
		background-color: #f8f9ff;
	}

	.group-info {
		cursor: pointer;
	}

	.group-info h4 {
		margin: 0 0 0.5rem 0;
		color: #333;
	}

	.group-info p {
		margin: 0 0 0.5rem 0;
		color: #666;
		font-size: 0.9rem;
	}

	.group-info small {
		color: #999;
		font-size: 0.8rem;
	}

	.group-actions {
		margin-top: 1rem;
		padding-top: 1rem;
		border-top: 1px solid #e9ecef;
	}

	.permission-name {
		font-weight: 600;
		color: #333;
	}

	.permission-description {
		color: #666;
		font-size: 0.9rem;
	}

	.resource, .action {
		font-size: 0.8rem;
		padding: 0.25rem 0.5rem;
		border-radius: 4px;
		font-family: 'Courier New', monospace;
	}

	.resource {
		background-color: #e9ecef;
		color: #495057;
	}

	.action {
		background-color: #d1ecf1;
		color: #0c5460;
	}

	/* Drawer Styles */
	.drawer-overlay {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: rgba(0, 0, 0, 0.5);
		z-index: 998;
		opacity: 0;
		visibility: hidden;
		transition: opacity 0.3s ease, visibility 0.3s ease;
	}

	.drawer-overlay.open {
		opacity: 1;
		visibility: visible;
	}

	.group-drawer, .user-drawer {
		position: fixed;
		top: 0;
		right: 0;
		width: 500px;
		height: 100%;
		background-color: white;
		box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
		transform: translateX(100%);
		transition: transform 0.3s ease;
		z-index: 999;
		display: flex;
		flex-direction: column;
	}

	.group-drawer.open, .user-drawer.open {
		transform: translateX(0);
	}

	.drawer-header {
		padding: 1.5rem;
		border-bottom: 1px solid #e9ecef;
		display: flex;
		justify-content: space-between;
		align-items: center;
		background-color: #f8f9fa;
	}

	.drawer-header h3 {
		margin: 0;
		color: #333;
		font-size: 1.3rem;
	}

	.close-btn {
		background: none;
		border: none;
		font-size: 1.5rem;
		cursor: pointer;
		color: #666;
		padding: 0.25rem;
		width: 32px;
		height: 32px;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 4px;
	}

	.close-btn:hover {
		background-color: #e9ecef;
		color: #333;
	}

	.drawer-tabs {
		display: flex;
		border-bottom: 1px solid #e9ecef;
		background-color: #f8f9fa;
	}

	.tab-btn {
		flex: 1;
		padding: 1rem;
		border: none;
		background: transparent;
		cursor: pointer;
		font-size: 0.9rem;
		color: #666;
		border-bottom: 2px solid transparent;
		transition: all 0.2s ease;
	}

	.tab-btn:hover {
		background-color: #e9ecef;
		color: #333;
	}

	.tab-btn.active {
		color: #007bff;
		border-bottom-color: #007bff;
		background-color: white;
		font-weight: 500;
	}

	.drawer-content {
		flex: 1;
		overflow-y: auto;
		padding: 0;
	}

	.tab-panel {
		padding: 1.5rem;
	}

	.tab-panel h4 {
		margin: 0 0 1rem 0;
		color: #333;
		font-size: 1.1rem;
	}

	.empty-state {
		text-align: center;
		padding: 2rem;
		color: #666;
	}

	.empty-state p {
		margin: 0 0 0.5rem 0;
		font-size: 1rem;
	}

	.empty-state small {
		color: #999;
		font-size: 0.9rem;
	}

	.members-list, .groups-list {
		display: grid;
		gap: 0.75rem;
	}

	.member-item, .group-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 1rem;
		background-color: #f8f9fa;
		border: 1px solid #e9ecef;
		border-radius: 6px;
	}

	.member-info, .group-info {
		display: flex;
		flex-direction: column;
		gap: 0.25rem;
	}

	.member-email, .group-name {
		font-weight: 500;
		color: #333;
	}

	.member-info small {
		color: #666;
		font-size: 0.8rem;
	}

	.group-description {
		color: #666;
		font-size: 0.9rem;
	}

	.permissions-list {
		display: flex;
		flex-direction: column;
		gap: 0.75rem;
	}

	.permission-item {
		padding: 1rem;
		background-color: #f8f9fa;
		border-radius: 8px;
		border-left: 4px solid #007bff;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.permission-info {
		flex: 1;
	}

	.permission-info .permission-name {
		font-weight: 600;
		color: #212529;
		margin-bottom: 0.25rem;
	}

	.permission-info .permission-description {
		color: #6c757d;
		font-size: 0.9rem;
		margin-bottom: 0.5rem;
	}

	.permission-details {
		display: flex;
		gap: 0.5rem;
	}

	@media (max-width: 900px) {
		.group-drawer, .user-drawer {
			width: 100%;
		}

		.tab-navigation {
			flex-wrap: wrap;
		}

		.tab-button {
			flex: 1;
			min-width: 120px;
		}
	}

	@media (max-width: 600px) {
		.data-table {
			font-size: 0.875rem;
		}

		th, td {
			padding: 0.5rem;
		}

		.member-item, .group-item, .permission-item {
			flex-direction: column;
			align-items: stretch;
			gap: 0.5rem;
		}

		.section-header {
			flex-direction: column;
			gap: 1rem;
			align-items: stretch;
		}

		.tab-btn {
			font-size: 0.8rem;
			padding: 0.75rem;
		}
	}
</style>
