package main

import (
	"os"
	"testing"
	"web-dashboard/backend/database"
	"web-dashboard/backend/testutils"
)

func TestMain(m *testing.M) {
	// Set up test environment using shared utilities
	testutils.SetupTestEnvironment()

	// Run tests
	code := m.Run()

	// Cleanup database connection if it exists
	if database.DB != nil {
		database.DB.Close()
	}

	// Exit with the test result code
	os.Exit(code)
}

func TestBasicFunctionality(t *testing.T) {
	t.Run("Environment Setup", func(t *testing.T) {
		if os.Getenv("TESTING") != "true" {
			t.<PERSON>r("TESTING environment variable not set")
		}
		t.Log("✅ Test environment properly configured")
	})
}
