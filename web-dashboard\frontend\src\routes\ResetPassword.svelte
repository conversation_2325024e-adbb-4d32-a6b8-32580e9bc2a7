<script>
  import { navigate } from 'svelte-routing';
  import { onMount } from 'svelte';

  let token = '';
  let newPassword = '';
  let confirmPassword = '';
  let error = '';
  let success = '';
  let loading = false;
  let tokenValid = false;
  let tokenChecked = false;

  onMount(() => {
    const urlParams = new URLSearchParams(window.location.search);
    token = urlParams.get('token');

    // Handle URL parameters for errors
    const urlError = urlParams.get('error');
    const urlMessage = urlParams.get('message');

    if (urlError && urlMessage) {
      error = decodeURIComponent(urlMessage);
      tokenValid = false;
      tokenChecked = true;
      
      // Clear the token from URL if there's an error to prevent confusion
      if (urlError === 'expired' || urlError === 'invalid') {
        const newUrl = window.location.pathname;
        window.history.replaceState({}, document.title, newUrl);
      }
      return;
    }

    if (!token) {
      error = 'Invalid or missing reset token. Please request a new password reset.';
      tokenValid = false;
      tokenChecked = true;
      return;
    }

    // If we have a token and no URL error, validate it
    validateToken();
  });

  async function validateToken() {
    try {
      // Make a request to validate the token (this will trigger backend validation)
      const response = await fetch(`/reset-password?token=${encodeURIComponent(token)}`, {
        method: 'GET'
      });

      // If response is not ok, it means token is invalid/expired
      if (!response.ok) {
        if (response.status === 429) {
          error = 'Too many reset attempts. Please try again later.';
        } else {
          error = 'This reset link is invalid or has expired. Please request a new password reset.';
        }
        tokenValid = false;
      } else {
        tokenValid = true;
      }
    } catch (err) {
      console.error('Token validation error:', err);
      error = 'Unable to validate reset link. Please try again later.';
      tokenValid = false;
    } finally {
      tokenChecked = true;
    }
  }

  async function handleResetPassword(event) {
    event.preventDefault();

    if (!token) {
      error = 'Invalid reset token';
      return;
    }

    if (newPassword !== confirmPassword) {
      error = 'Passwords do not match';
      return;
    }

    if (newPassword.length < 6) {
      error = 'Password must be at least 6 characters long';
      return;
    }

    loading = true;
    error = '';
    success = '';

    const formData = new FormData();
    formData.append('token', token);
    formData.append('new_password', newPassword);

    try {
      const response = await fetch('/reset-password', {
        method: 'POST',
        body: formData
      });

      if (response.ok) {
        const result = await response.json();
        success = result.message || 'Password reset successfully!';

        // Redirect to auth page after successful reset
        setTimeout(() => {
          navigate('/auth');
        }, 3000);
      } else {
        const errorText = await response.text();
        error = errorText || 'Failed to reset password';
      }
    } catch (err) {
      console.error('Reset password error:', err);
      error = 'Network error. Please try again.';
    } finally {
      loading = false;
    }
  }
</script>

<div class="reset-container">
  <div class="reset-card">
    <h1>Reset Password</h1>

    {#if error}
      <div class="error-message">{error}</div>
    {/if}

    {#if success}
      <div class="success-message">
        {success}
        <p>Redirecting to login page...</p>
      </div>
    {:else if tokenChecked && tokenValid}
      <form class="reset-form" on:submit={handleResetPassword}>
        <div class="form-group">
          <label for="newPassword">New Password</label>
          <input
            type="password"
            id="newPassword"
            bind:value={newPassword}
            required
            disabled={loading}
            minlength="6"
          />
        </div>

        <div class="form-group">
          <label for="confirmPassword">Confirm New Password</label>
          <input
            type="password"
            id="confirmPassword"
            bind:value={confirmPassword}
            required
            disabled={loading}
          />
        </div>

        <button type="submit" class="submit-btn" disabled={loading}>
          {loading ? 'Resetting...' : 'Reset Password'}
        </button>
      </form>
    {:else if !tokenChecked}
      <div class="loading-message">Validating reset link...</div>
    {/if}

    <div class="back-to-login">
      <button class="back-link" on:click={() => navigate('/auth')}>Back to Login</button>
    </div>
  </div>
</div>

<style>
  .reset-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 20px;
    background-color: #f5f5f5;
  }

  .reset-card {
    background: white;
    padding: 40px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 400px;
  }

  h1 {
    text-align: center;
    margin-bottom: 30px;
    color: #333;
  }

  .error-message {
    background-color: #fee;
    color: #c33;
    padding: 10px;
    border-radius: 4px;
    margin-bottom: 20px;
    text-align: center;
  }

  .success-message {
    background-color: #efe;
    color: #393;
    padding: 10px;
    border-radius: 4px;
    margin-bottom: 20px;
    text-align: center;
  }

  .success-message p {
    margin: 10px 0 0 0;
    font-size: 14px;
  }

  .loading-message {
    text-align: center;
    padding: 20px;
    color: #666;
    font-style: italic;
  }

  .reset-form {
    margin-bottom: 20px;
  }

  .form-group {
    margin-bottom: 15px;
  }

  label {
    display: block;
    margin-bottom: 5px;
    color: #333;
    font-weight: 500;
  }

  input {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
    box-sizing: border-box;
  }

  input:focus {
    outline: none;
    border-color: #4285f4;
  }

  input:disabled {
    background-color: #f5f5f5;
    cursor: not-allowed;
  }

  .submit-btn {
    width: 100%;
    padding: 12px;
    background-color: #4285f4;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.2s;
  }

  .submit-btn:hover:not(:disabled) {
    background-color: #3367d6;
  }

  .submit-btn:disabled {
    background-color: #ccc;
    cursor: not-allowed;
  }

  .back-to-login {
    text-align: center;
    margin-top: 20px;
  }

  .back-link {
    color: #4285f4;
    background: none;
    border: none;
    font-size: 14px;
    cursor: pointer;
    text-decoration: none;
  }

  .back-link:hover {
    text-decoration: underline;
  }
</style>