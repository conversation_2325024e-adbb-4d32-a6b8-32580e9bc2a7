package auth

import (
	"testing"

	"web-dashboard/backend/database"
	"web-dashboard/backend/users"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestGenerateSessionID(t *testing.T) {
	// Test multiple generations to ensure uniqueness
	sessions := make(map[string]bool)

	for i := 0; i < 100; i++ {
		sessionID, err := generateSessionID()
		assert.NoError(t, err)
		assert.NotEmpty(t, sessionID)
		assert.Equal(t, 64, len(sessionID)) // 32 bytes = 64 hex characters

		// Ensure uniqueness
		assert.False(t, sessions[sessionID], "Session ID should be unique")
		sessions[sessionID] = true
	}
}

func TestCreateSession(t *testing.T) {
	// Create a test user
	userEmail := "<EMAIL>"
	userID, err := users.CreateUser(userEmail, "password123", true, true)
	require.NoError(t, err)

	tests := []struct {
		name         string
		userID       int
		sessionToken string
		expectError  bool
	}{
		{
			name:         "valid session creation",
			userID:       userID,
			sessionToken: "valid-session-token-123",
			expectError:  false,
		},
		{
			name:         "invalid user ID",
			userID:       99999,
			sessionToken: "invalid-user-session",
			expectError:  true,
		},
		{
			name:         "empty session token",
			userID:       userID,
			sessionToken: "",
			expectError:  false, // Database might allow empty string
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := CreateSession(tt.userID, tt.sessionToken)

			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)

				// Verify session was created
				if tt.sessionToken != "" {
					session, err := GetSession(tt.sessionToken)
					assert.NoError(t, err)
					assert.NotNil(t, session)
					assert.Equal(t, tt.userID, session.UserID)
					assert.Equal(t, tt.sessionToken, session.SessionToken)

					// Clean up
					InvalidateSession(tt.sessionToken)
				}
			}
		})
	}

	// Cleanup
	database.DB.Exec("DELETE FROM users WHERE email = $1", userEmail)
}

func TestGetSession(t *testing.T) {
	// Cleanup any existing test user first
	userEmail := "<EMAIL>"
	database.DB.Exec("DELETE FROM users WHERE email = $1", userEmail)

	// Create a test user
	userID, err := users.CreateUser(userEmail, "password123", true, true)
	require.NoError(t, err)

	sessionToken := "test-get-session-token"
	err = CreateSession(userID, sessionToken)
	require.NoError(t, err)

	tests := []struct {
		name         string
		sessionToken string
		expectError  bool
		expectedUser int
	}{
		{
			name:         "valid session token",
			sessionToken: sessionToken,
			expectError:  false,
			expectedUser: userID,
		},
		{
			name:         "invalid session token",
			sessionToken: "nonexistent-token",
			expectError:  true,
		},
		{
			name:         "empty session token",
			sessionToken: "",
			expectError:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			session, err := GetSession(tt.sessionToken)

			if tt.expectError {
				assert.Error(t, err)
				assert.Nil(t, session)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, session)
				assert.Equal(t, tt.expectedUser, session.UserID)
				assert.Equal(t, tt.sessionToken, session.SessionToken)
			}
		})
	}

	// Cleanup
	InvalidateSession(sessionToken)
	database.DB.Exec("DELETE FROM users WHERE email = $1", userEmail)
}

func TestInvalidateSession(t *testing.T) {
	// Cleanup any existing test user first
	userEmail := "<EMAIL>"
	database.DB.Exec("DELETE FROM users WHERE email = $1", userEmail)

	// Create a test user
	userID, err := users.CreateUser(userEmail, "password123", true, true)
	require.NoError(t, err)

	sessionToken := "test-invalidate-token"
	err = CreateSession(userID, sessionToken)
	require.NoError(t, err)

	// Verify session exists
	session, err := GetSession(sessionToken)
	assert.NoError(t, err)
	assert.NotNil(t, session)

	tests := []struct {
		name         string
		sessionToken string
		expectError  bool
	}{
		{
			name:         "invalidate existing session",
			sessionToken: sessionToken,
			expectError:  false,
		},
		{
			name:         "invalidate nonexistent session",
			sessionToken: "nonexistent-token",
			expectError:  false, // DELETE operations don't error if no rows affected
		},
		{
			name:         "invalidate empty session token",
			sessionToken: "",
			expectError:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := InvalidateSession(tt.sessionToken)

			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)

				// Verify session is gone (only for the first test)
				if tt.sessionToken == sessionToken {
					_, err := GetSession(tt.sessionToken)
					assert.Error(t, err) // Should not be found
				}
			}
		})
	}

	// Cleanup
	database.DB.Exec("DELETE FROM users WHERE email = $1", userEmail)
}

func TestInvalidateAllUserSessions(t *testing.T) {
	// Create a test user
	userEmail := "<EMAIL>"
	userID, err := users.CreateUser(userEmail, "password123", true, true)
	require.NoError(t, err)

	// Create multiple sessions for the user
	sessionTokens := []string{
		"session-1-token",
		"session-2-token",
		"session-3-token",
	}

	for _, token := range sessionTokens {
		err = CreateSession(userID, token)
		require.NoError(t, err)
	}

	// Verify all sessions exist
	for _, token := range sessionTokens {
		session, err := GetSession(token)
		assert.NoError(t, err)
		assert.NotNil(t, session)
	}

	tests := []struct {
		name        string
		userID      int
		expectError bool
	}{
		{
			name:        "invalidate all sessions for valid user",
			userID:      userID,
			expectError: false,
		},
		{
			name:        "invalidate all sessions for nonexistent user",
			userID:      99999,
			expectError: false, // DELETE operations don't error if no rows affected
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := InvalidateAllUserSessions(tt.userID)

			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)

				// For valid user, verify all sessions are gone
				if tt.userID == userID {
					for _, token := range sessionTokens {
						_, err := GetSession(token)
						assert.Error(t, err) // Should not be found
					}
				}
			}
		})
	}

	// Cleanup
	database.DB.Exec("DELETE FROM users WHERE email = $1", userEmail)
}

func TestCleanupExpiredSessions(t *testing.T) {
	// Create a test user
	userEmail := "<EMAIL>"
	userID, err := users.CreateUser(userEmail, "password123", true, true)
	require.NoError(t, err)

	// Create a session
	sessionToken := "cleanup-session-token"
	err = CreateSession(userID, sessionToken)
	require.NoError(t, err)

	tests := []struct {
		name        string
		maxAge      string
		expectError bool
	}{
		{
			name:        "cleanup with valid interval",
			maxAge:      "1 day",
			expectError: false,
		},
		{
			name:        "cleanup with short interval",
			maxAge:      "1 minute",
			expectError: false,
		},
		{
			name:        "cleanup with zero interval",
			maxAge:      "0 seconds",
			expectError: false,
		},
		{
			name:        "cleanup with invalid interval",
			maxAge:      "invalid",
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := CleanupExpiredSessions(tt.maxAge)

			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}

	// Cleanup
	InvalidateSession(sessionToken)
	database.DB.Exec("DELETE FROM users WHERE email = $1", userEmail)
}

func TestSessionLifecycle(t *testing.T) {
	// Cleanup any existing test user first
	userEmail := "<EMAIL>"
	database.DB.Exec("DELETE FROM users WHERE email = $1", userEmail)

	// Create a test user
	userID, err := users.CreateUser(userEmail, "password123", true, true)
	require.NoError(t, err)

	// Generate session ID
	sessionID, err := generateSessionID()
	require.NoError(t, err)
	assert.NotEmpty(t, sessionID)

	// Create session
	err = CreateSession(userID, sessionID)
	require.NoError(t, err)

	// Retrieve session
	session, err := GetSession(sessionID)
	require.NoError(t, err)
	assert.Equal(t, userID, session.UserID)
	assert.Equal(t, sessionID, session.SessionToken)

	// Invalidate session
	err = InvalidateSession(sessionID)
	require.NoError(t, err)

	// Verify session is gone
	_, err = GetSession(sessionID)
	assert.Error(t, err)

	// Cleanup
	database.DB.Exec("DELETE FROM users WHERE email = $1", userEmail)
}