
package api

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"web-dashboard/backend/testutils"

	"github.com/gorilla/mux"
)

func TestUsersRoutes(t *testing.T) {
	testutils.SetupTestEnvironment()

	router := mux.NewRouter()
	AttachUserRoutes(router)

	t.Run("GET /user/users without auth returns 401", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/user/users", nil)
		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		if rr.Code != http.StatusUnauthorized && rr.Code != http.StatusNotFound {
			t.Errorf("Expected 401 or 404, got %d", rr.Code)
		}
	})
}
