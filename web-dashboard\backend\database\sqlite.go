package database

import (
	"database/sql"
	"fmt"

	_ "github.com/mattn/go-sqlite3"
)

type DBSQLite struct {
	conn *sql.DB
}

func (db *DBSQLite) Connect() error {
	var err error
	db.conn, err = sql.Open("sqlite3", "./app/data/chromedata.db")
	if err != nil {
		return fmt.Errorf("failed to connect to database: %s", err)
	}
	return nil
}

func (db *DBSQLite) RebuildSchema() error {
	return nil
}

func (db *DBSQLite) RunQuery(query string) error {
	_, err := db.conn.Exec(query)
	if err != nil {
		return fmt.Errorf("failed to run query: %s", err)
	}
	return nil
}

/*
func (db *DBSQLite) RebuildTable(object IFPSchema, schema string) error {
	_, err := db.conn.Exec(object.GetCreateString(schema, "sqlite"))
	if err != nil {
		return fmt.Errorf("failed to rebuild table (%s): %s", object.GetTypeName(), err)
	}

	return nil
}

func (db *DBSQLite) ProcessFileToTable(object IFPSchema, path string) error {
	// Import data from file
	records, err := utils.ReadCSV(path)
	if err != nil {
		return fmt.Errorf("failed to read CSV: %s", err)
	}

	start := time.Now()
	err = db.InsertIntoTable(object, records)
	if err != nil {
		return fmt.Errorf("failed to insert into table (%s): %s", object.GetTypeName(), err)
	}

	elapsed := time.Since(start)
	log.Println(object.GetTypeName(), "processed:", elapsed)
	start = time.Now()

	elapsed = time.Since(start)
	log.Println(object.GetTypeName(), "merged:", elapsed)

	return nil
}

func (db *DBSQLite) InsertIntoTable(object IFPSchema, records [][]string) error {
	tx, err := db.conn.Begin()
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %s", err)
	}

	stmt, err := tx.Prepare(object.GetInsertString("sqlite"))
	if err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to prepare statement: %s", err)
	}
	defer stmt.Close()

	for _, record := range records {
		r := Record{
			Fields: make([]interface{}, len(record)),
		}
		fields := object.GetFields()

		for i, field := range record {
			switch fields[i] {
			case "int":
				if field == "" {
					r.Fields[i] = nil
					continue
				}
				r.Fields[i], err = strconv.Atoi(field)
				if err != nil {
					log.Println("Error converting:", err)
					return fmt.Errorf("failed to convert %s to int", field)
				}
			case "float":
				if field == "" {
					r.Fields[i] = nil
					continue
				}
				r.Fields[i], err = strconv.ParseFloat(field, 64)
				if err != nil {
					return fmt.Errorf("failed to convert %s to float", field)
				}
			case "string":
				if !utf8.ValidString(field) {
					field, err = utils.EncodeWindows1252ToUTF8(field)
					if err != nil {
						return fmt.Errorf("failed to convert %s to utf8", field)
					}
				}
				r.Fields[i] = field
			case "date":
				if field == "" {
					r.Fields[i] = nil
					continue
				}
				r.Fields[i], err = time.Parse("01/02/2006", field)
				if err != nil {
					return fmt.Errorf("failed to convert %s to date", field)
				}
			default:
				return fmt.Errorf("unsupported data type: %s", fields[i])
			}
		}
		_, err = stmt.Exec(r.Fields...)
		if err != nil {
			tx.Rollback()
			return fmt.Errorf("failed to insert into table (%s): %s", object.GetTypeName(), err)
		}
	}

	err = tx.Commit()
	if err != nil {
		return fmt.Errorf("failed to commit transaction: %s", err)
	}

	return nil
}
*/
