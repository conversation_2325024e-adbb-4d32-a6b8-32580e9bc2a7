# These are some examples of commonly ignored file patterns.
# You should customize this list as applicable to your project.
# Learn more about .gitignore:
#     https://www.atlassian.com/git/tutorials/saving-changes/gitignore

# Node artifact files
node_modules/
dist/

# Compiled Java class files
*.class

# Compiled Python bytecode
*.py[cod]

# Log files
*.log

# Package files
*.jar

# Maven
target/
dist/

# JetBrains IDE
.idea/

# Unit test reports
TEST*.xml

# Generated by MacOS
.DS_Store

# Generated by Windows
Thumbs.db

# Applications
*.app
*.exe
*.war

# Large media files
*.mp4
*.tiff
*.avi
*.flv
*.mov
*.wmv

# Go binaries
web-dashboard/backend/backend

# Redis dump files
*.rdb
dump.rdb

# Go telemetry data
.config/

# VSCode settings
.vscode/

# Attached assets (logs/debug files)
attached_assets/

# Temporary files
cookies.txt

*.env
web-dashboard/backend/emailServiceAccount.json
web-dashboard/frontend/public/build/*
web-dashboard/backend/auth.json

web-dashboard/backend/test_output.tmp