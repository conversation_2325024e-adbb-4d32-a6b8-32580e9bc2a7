
package api

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"web-dashboard/backend/testutils"

	"github.com/gorilla/mux"
)

func TestPermissionsRoutes(t *testing.T) {
	testutils.SetupTestEnvironment()

	router := mux.NewRouter()
	AttachPermissionRoutes(router)

	t.Run("GET /permission/permissions without auth returns 401", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/permission/permissions", nil)
		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		if rr.Code != http.StatusUnauthorized && rr.Code != http.StatusNotFound {
			t.Errorf("Expected 401 or 404, got %d", rr.Code)
		}
	})
}
