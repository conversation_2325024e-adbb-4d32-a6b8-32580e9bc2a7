<script>
  import RedisManager from './RedisManager.svelte';

  export let userInfo = null;

  let loading = true;
  let error = null;
  let hasPermission = false;
  let activeTab = 'system';

  // System Settings state
  let systemSettings = [];
  let loadingSettings = false;
  let settingsError = null;
  let hasSystemSettingsPermission = false;
  let editingSettings = {}; // Track which settings are being edited
  let editValues = {}; // Store temporary edit values

  // System information
  let systemInfo = null;
  let loadingSystemInfo = false;

  // Reactive statement that runs when userInfo prop changes
  $: {
    if (userInfo) {
      initializeComponent();
    } else {
      loading = false;
      error = 'User information not available';
    }
  }

  async function initializeComponent() {
    try {
      // Check if user has basic system access permissions
      const response = await fetch('/api/user/info');
      if (response.ok) {
        hasPermission = true;
        await loadSystemInfo();
        await checkSystemSettingsPermission();
      } else {
        hasPermission = false;
        error = 'Access denied - insufficient permissions';
      }
    } catch (err) {
      error = 'Failed to initialize system view';
      console.error('Error initializing system view:', err);
    } finally {
      loading = false;
    }
  }

  async function checkSystemSettingsPermission() {
    try {
      const response = await fetch('/api/system-settings/permissions/check', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ action: 'view' })
      });

      if (response.ok) {
        const data = await response.json();
        hasSystemSettingsPermission = data.has_permission;
        if (hasSystemSettingsPermission) {
          await loadSystemSettings();
          await loadDebugLevels();
        }
      }
    } catch (err) {
      console.error('Failed to check system settings permission:', err);
    }
  }

  async function loadSystemSettings() {
    loadingSettings = true;
    settingsError = null;
    try {
      const response = await fetch('/api/system-settings/settings');
      if (response.ok) {
        systemSettings = await response.json();
      } else {
        settingsError = 'Failed to load system settings';
      }
    } catch (err) {
      settingsError = 'Error loading system settings: ' + err.message;
      console.error('Failed to load system settings:', err);
    } finally {
      loadingSettings = false;
    }
  }

  async function updateSetting(key, value, category, dataType) {
    try {
      const response = await fetch(`/api/system-settings/settings/${key}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          key: key,
          value: value,
          category: category,
          value_type: dataType
        })
      });

      if (response.ok) {
        await loadSystemSettings();
        return true;
      } else {
        throw new Error('Failed to update setting');
      }
    } catch (err) {
      console.error('Error updating setting:', err);
      settingsError = 'Failed to update setting: ' + err.message;
      return false;
    }
  }

  function startEditing(setting) {
    editingSettings[setting.key] = true;
    if (setting.data_type === 'boolean') {
      editValues[setting.key] = setting.value ? 1 : 0;
    } else {
      editValues[setting.key] = setting.value;
    }
    editingSettings = { ...editingSettings };
    editValues = { ...editValues };
  }

  function cancelEditing(key) {
    delete editingSettings[key];
    delete editValues[key];
    editingSettings = { ...editingSettings };
    editValues = { ...editValues };
  }

  async function saveEdit(setting) {
    const newValue = editValues[setting.key];
    let convertedValue = newValue;

    // Convert value based on data type
    if (setting.data_type === 'boolean') {
      convertedValue = newValue == 1 || newValue === true;
    } else if (setting.data_type === 'integer') {
      convertedValue = parseInt(newValue, 10);
      if (isNaN(convertedValue)) {
        settingsError = 'Invalid integer value';
        return;
      }
    } else if (setting.data_type === 'float') {
      convertedValue = parseFloat(newValue);
      if (isNaN(convertedValue)) {
        settingsError = 'Invalid float value';
        return;
      }
    }

    const success = await updateSetting(setting.key, convertedValue, setting.category, setting.data_type);
    if (success) {
      delete editingSettings[setting.key];
      delete editValues[setting.key];
      editingSettings = { ...editingSettings };
      editValues = { ...editValues };
    }
  }

  function groupSettingsByCategory(settings) {
    const grouped = {};
    settings.forEach(setting => {
      if (!grouped[setting.category]) {
        grouped[setting.category] = [];
      }
      grouped[setting.category].push(setting);
    });
    return grouped;
  }

  // Debug levels state
  let debugLevels = [];
  let loadingDebugLevels = false;

  async function loadDebugLevels() {
    loadingDebugLevels = true;
    try {
      const response = await fetch('/api/system-settings/debug-levels');
      if (response.ok) {
        debugLevels = await response.json();
      }
    } catch (err) {
      console.error('Failed to load debug levels:', err);
    } finally {
      loadingDebugLevels = false;
    }
  }

  async function loadSystemInfo() {
    loadingSystemInfo = true;
    try {
      // This would typically fetch system information from the backend
      // For now, we'll show some basic information
      systemInfo = {
        applicationName: 'Web Dashboard',
        version: '1.0.0',
        environment: 'Development',
        serverTime: new Date().toISOString(),
        uptime: 'Unknown'
      };
    } catch (err) {
      console.error('Failed to load system information:', err);
    } finally {
      loadingSystemInfo = false;
    }
  }

  function formatDate(dateString) {
    return new Date(dateString).toLocaleString();
  }

  function setActiveTab(tab) {
    activeTab = tab;
  }
</script>

<div class="system-container">
  <h1>System Management</h1>

  {#if loading}
    <div class="loading">Loading system interface...</div>
  {:else if error}
    <div class="error">
      <p>{error}</p>
      <button on:click={() => window.location.reload()}>Retry</button>
    </div>
  {:else if !hasPermission}
    <div class="access-denied">
      <h2>Access Denied</h2>
      <p>You don't have permission to access system management.</p>
      <p>Please contact your administrator if you believe this is an error.</p>
      <button class="secondary-button" on:click={() => window.history.back()}>
        ← Back
      </button>
    </div>
  {:else}
    <!-- Tab Navigation -->
    <div class="tab-navigation">
      <button 
        class="tab-button" 
        class:active={activeTab === 'system'}
        on:click={() => setActiveTab('system')}
      >
        System Information
      </button>
      <button 
        class="tab-button" 
        class:active={activeTab === 'redis'}
        on:click={() => setActiveTab('redis')}
      >
        Redis Management
      </button>
      {#if hasSystemSettingsPermission}
        <button 
          class="tab-button" 
          class:active={activeTab === 'settings'}
          on:click={() => setActiveTab('settings')}
        >
          System Settings
        </button>
      {/if}
    </div>

    <!-- Tab Content -->
    <div class="tab-content">
      {#if activeTab === 'system'}
        <div class="system-card">
          <div class="system-section">
            <h2>Application Information</h2>
            <p>Basic information about the system and application.</p>

            {#if loadingSystemInfo}
              <div class="loading">Loading...</div>
            {:else if systemInfo}
              <div class="info-grid">
                <div class="info-item">
                  <label>Application Name:</label>
                  <span>{systemInfo.applicationName}</span>
                </div>
                <div class="info-item">
                  <label>Version:</label>
                  <span>{systemInfo.version}</span>
                </div>
                <div class="info-item">
                  <label>Environment:</label>
                  <span class="environment">{systemInfo.environment}</span>
                </div>
                <div class="info-item">
                  <label>Server Time:</label>
                  <span>{formatDate(systemInfo.serverTime)}</span>
                </div>
                <div class="info-item">
                  <label>System Uptime:</label>
                  <span>{systemInfo.uptime}</span>
                </div>
              </div>
            {/if}
          </div>

          <div class="system-section">
            <h2>User Information</h2>
            <p>Information about your current session.</p>

            <div class="info-grid">
              <div class="info-item">
                <label>Email:</label>
                <span>{userInfo?.email || 'Not available'}</span>
              </div>
              <div class="info-item">
                <label>User ID:</label>
                <span>{userInfo?.id || 'Not available'}</span>
              </div>
              <div class="info-item">
                <label>Access Level:</label>
                <span class="access-level">User</span>
              </div>
            </div>
          </div>

          <div class="system-actions">
            <button class="secondary-button" on:click={() => window.history.back()}>
              ← Back to Dashboard
            </button>
          </div>
        </div>
      {:else if activeTab === 'redis'}
        <RedisManager {userInfo} />
      {:else if activeTab === 'settings'}
        <div class="system-card">
          <div class="system-section">
            <h2>System Settings</h2>
            <p>Manage system configuration and settings.</p>

            {#if loadingSettings}
              <div class="loading">Loading settings...</div>
            {:else if settingsError}
              <div class="error">
                <p>{settingsError}</p>
                <button on:click={() => loadSystemSettings()}>Retry</button>
              </div>
            {:else if systemSettings.length === 0}
              <div class="no-data">
                <p>No system settings found.</p>
              </div>
            {:else}
              {#each Object.entries(groupSettingsByCategory(systemSettings)) as [category, settings]}
                <div class="settings-category">
                  <h3>{category.charAt(0).toUpperCase() + category.slice(1)} Settings</h3>
                  <div class="settings-grid">
                    {#each settings as setting}
                      <div class="setting-item">
                        <div class="setting-info">
                          <span class="setting-description">{setting.description || 'No description'}</span>
                        </div>
                        <div class="setting-value-container">
                          <div class="setting-input-area">
                            {#if editingSettings[setting.key]}
                              {#if setting.key === 'debug_level' && debugLevels.length > 0}
                                <select 
                                  bind:value={editValues[setting.key]} 
                                  class="setting-input"
                                >
                                  {#each debugLevels as level}
                                    <option value={level.value}>{level.label}</option>
                                  {/each}
                                </select>
                              {:else if setting.data_type === 'boolean'}
                                <div class="button b2" class:checked={editValues[setting.key] == 1}>
                                  <input 
                                    type="checkbox" 
                                    class="checkbox" 
                                    checked={editValues[setting.key] == 1}
                                    on:change={() => editValues[setting.key] = editValues[setting.key] == 1 ? 0 : 1}
                                  />
                                  <div class="knobs">
                                    <span>ENABLED</span>
                                  </div>
                                  <div class="layer"></div>
                                </div>
                              {:else if setting.data_type === 'integer'}
                                <input 
                                  type="number" 
                                  step="1"
                                  bind:value={editValues[setting.key]} 
                                  class="setting-input"
                                />
                              {:else if setting.data_type === 'float'}
                                <input 
                                  type="number" 
                                  step="0.01"
                                  bind:value={editValues[setting.key]} 
                                  class="setting-input"
                                />
                              {:else}
                                <input 
                                  type="text" 
                                  bind:value={editValues[setting.key]} 
                                  class="setting-input"
                                />
                              {/if}
                            {:else}
                              {#if setting.key === 'debug_level'}
                                <span class="setting-value debug-level">
                                  {#if debugLevels.length > 0}
                                    {debugLevels.find(level => level.value === setting.value)?.label || setting.value}
                                  {:else}
                                    {setting.value}
                                  {/if}
                                </span>
                              {:else if setting.data_type === 'boolean'}
                                <div class="button b2 disabled" class:checked={setting.value}>
                                  <input 
                                    type="checkbox" 
                                    class="checkbox" 
                                    checked={setting.value}
                                    disabled
                                  />
                                  <div class="knobs">
                                    <span>ENABLED</span>
                                  </div>
                                  <div class="layer"></div>
                                </div>
                              {:else}
                                <span class="setting-value" class:number={setting.data_type === 'integer' || setting.data_type === 'float'}>
                                  {setting.value}
                                </span>
                              {/if}
                            {/if}
                          </div>
                          <div class="setting-buttons">
                            {#if editingSettings[setting.key]}
                              <button class="save-button" on:click={() => saveEdit(setting)}>
                                Save
                              </button>
                              <button class="cancel-button" on:click={() => cancelEditing(setting.key)}>
                                Cancel
                              </button>
                            {:else}
                              <button class="edit-button" on:click={() => startEditing(setting)}>
                                Edit
                              </button>
                            {/if}
                          </div>
                        </div>
                      </div>
                    {/each}
                  </div>
                </div>
              {/each}
            {/if}
          </div>
        </div>
      {/if}
    </div>
  {/if}
</div>

<style>
  .system-container {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 0 1rem;
  }

  h1 {
    color: #333;
    margin-bottom: 2rem;
    text-align: center;
  }

  .loading {
    text-align: center;
    padding: 2rem;
    color: #666;
  }

  .error {
    text-align: center;
    padding: 2rem;
    color: #dc3545;
  }

  .error button {
    margin-top: 1rem;
    padding: 0.5rem 1rem;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
  }

  .access-denied {
    text-align: center;
    padding: 3rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  }

  .access-denied h2 {
    color: #dc3545;
    margin-bottom: 1rem;
  }

  .access-denied p {
    color: #666;
    margin-bottom: 1rem;
  }

  .tab-navigation {
    display: flex;
    background: white;
    border-radius: 8px 8px 0 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
    margin-bottom: 0;
  }

  .tab-button {
    flex: 1;
    padding: 1rem 2rem;
    background: #f8f9fa;
    border: none;
    border-bottom: 3px solid transparent;
    cursor: pointer;
    font-weight: 500;
    color: #666;
    transition: all 0.3s ease;
  }

  .tab-button:hover {
    background: #e9ecef;
    color: #495057;
  }

  .tab-button.active {
    background: white;
    color: #007bff;
    border-bottom-color: #007bff;
  }

  .tab-content {
    background: white;
    border-radius: 0 0 8px 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    min-height: 400px;
  }

  .system-card {
    background: transparent;
    border-radius: 0;
    box-shadow: none;
    overflow: hidden;
  }

  .system-section {
    padding: 2rem;
    border-bottom: 1px solid #f0f0f0;
  }

  .system-section:last-of-type {
    border-bottom: none;
  }

  .system-section h2 {
    margin: 0 0 1rem 0;
    color: #333;
    font-size: 1.4rem;
  }

  .system-section p {
    color: #666;
    margin-bottom: 1.5rem;
  }

  .info-grid {
    display: grid;
    gap: 1rem;
  }

  .info-item {
    display: grid;
    grid-template-columns: 200px 1fr;
    gap: 1rem;
    align-items: center;
  }

  .info-item label {
    font-weight: 600;
    color: #555;
    margin: 0;
  }

  .info-item span {
    color: #333;
    background-color: #f8f9fa;
    padding: 0.5rem;
    border-radius: 4px;
  }

  .environment {
    background-color: #fff3cd !important;
    color: #856404 !important;
    font-weight: 500;
  }

  .access-level {
    background-color: #d4edda !important;
    color: #155724 !important;
    font-weight: 500;
  }

  .secondary-button {
    padding: 0.75rem 1.5rem;
    background-color: #6c757d;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
  }

  .secondary-button:hover {
    background-color: #5a6268;
  }

  .system-actions {
    padding: 2rem;
    background-color: #f8f9fa;
    text-align: center;
  }

  .settings-category {
    margin-bottom: 2rem;
  }

  .settings-category h3 {
    margin: 0 0 1rem 0;
    color: #495057;
    font-size: 1.2rem;
    border-bottom: 2px solid #007bff;
    padding-bottom: 0.5rem;
  }

  .settings-grid {
    display: grid;
    gap: 1rem;
  }

  .setting-item {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 1rem;
    align-items: center;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
  }

  .setting-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
  }

  .setting-key {
    font-weight: 600;
    color: #333;
    font-size: 0.95rem;
  }

  .setting-description {
    font-weight: 600;
    color: #333;
    font-size: 0.95rem;
  }

  .setting-value-container {
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  .setting-input-area {
    width: 200px;
    min-width: 200px;
    max-width: 200px;
  }

  .setting-value {
    padding: 0.5rem 0.75rem;
    background-color: #fff;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    text-align: center;
    display: block;
    width: 100%;
    height: 38px;
    box-sizing: border-box;
    line-height: 1.4;
  }

  .setting-input {
    padding: 0.5rem 0.75rem;
    border: 1px solid #007bff;
    border-radius: 4px;
    font-size: 0.9rem;
    width: 100%;
    height: 38px;
    box-sizing: border-box;
    background-color: #fff;
  }

  .setting-input:focus {
    outline: none;
    border-color: #0056b3;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
  }

  .button {
    position: relative;
    width: 100%;
    height: 38px;
    margin: 0;
    overflow: hidden;
    flex-shrink: 0;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    background-color: #fff;
    box-sizing: border-box;
  }

  .button.disabled {
    opacity: 0.7;
    pointer-events: none;
  }

  .checkbox {
    position: relative;
    width: 100%;
    height: 100%;
    padding: 0;
    margin: 0;
    opacity: 0;
    cursor: pointer;
    z-index: 3;
  }

  .knobs {
    z-index: 2;
  }

  .layer {
    width: 100%;
    background-color: #fcebeb;
    transition: 0.3s ease all;
    z-index: 1;
  }

  .button .knobs,
  .button .layer {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
  }

  .button .knobs:before,
  .button .knobs:after,
  .button .knobs span {
    position: absolute;
    top: 4px;
    width: 20px;
    height: 10px;
    font-size: 10px;
    font-weight: bold;
    text-align: center;
    line-height: 1;
    padding: 9px 4px;
    border-radius: 2px;
    transition: 0.3s ease all;
  }

  .button .knobs:before {
    content: "";
    left: calc(50% - 4px);
    background-color: #f44336;
    width: calc(50% - 8px);
  }

  .button .knobs:after {
    content: "DISABLED";
    right: 4px;
    color: #fff;
    width: calc(50% - 8px);
  }

  .button .knobs span {
    display: inline-block;
    left: 4px;
    color: #4e4e4e;
    z-index: 1;
    width: calc(50% - 8px);
  }

  .button.checked .knobs span {
    color: #fff;
  }

  .button.checked .knobs:before {
    left: 4px;
    background-color: #03a9f4;
    width: calc(50% - 8px);
  }

  .button.checked .knobs:after {
    color: #4e4e4e;
    content: "ENABLED";
  }

  .button.checked .layer {
    background-color: #e6f3ff;
  }



  .setting-value.number {
    text-align: right;
    color: #007bff;
  }

  .setting-value.debug-level {
    color: #6f42c1;
    font-weight: 500;
  }

  .setting-buttons {
    display: flex;
    gap: 0.5rem;
    width: 120px;
    min-width: 120px;
    max-width: 120px;
    justify-content: flex-end;
  }

  .edit-button {
    padding: 0.4rem 0.8rem;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.85rem;
    transition: background-color 0.2s;
    margin: 0;
  }

  .edit-button:hover {
    background-color: #0056b3;
  }

  .save-button {
    padding: 0.4rem 0.8rem;
    background-color: #28a745;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.85rem;
    transition: background-color 0.2s;
    margin: 0;
  }

  .save-button:hover {
    background-color: #218838;
  }

  .cancel-button {
    padding: 0.4rem 0.8rem;
    background-color: #6c757d;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.85rem;
    transition: background-color 0.2s;
    margin: 0;
  }

  .cancel-button:hover {
    background-color: #5a6268;
  }

  .no-data {
    text-align: center;
    padding: 2rem;
    color: #666;
  }

  @media (max-width: 768px) {
    .tab-navigation {
      flex-direction: column;
    }

    .tab-button {
      border-bottom: 1px solid #e9ecef;
      border-right: none;
    }

    .tab-button.active {
      border-bottom-color: #007bff;
      border-right: none;
    }

    .info-item {
      grid-template-columns: 1fr;
      gap: 0.5rem;
    }

    .setting-item {
      grid-template-columns: 1fr;
      gap: 0.75rem;
    }

    .setting-value-container {
      justify-content: space-between;
    }
  }



  .button.checked .knobs:after {
    content: "DISABLED";
  }
</style>