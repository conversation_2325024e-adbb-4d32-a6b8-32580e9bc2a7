
package api

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"web-dashboard/backend/testutils"

	"github.com/gorilla/mux"
)

func TestRedisRoutes(t *testing.T) {
	testutils.SetupTestEnvironment()

	router := mux.NewRouter()
	AttachRedisRoutes(router)

	t.Run("GET /redis/stats without auth returns 401", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/redis/stats", nil)
		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		if rr.Code != http.StatusUnauthorized && rr.Code != http.StatusNotFound {
			t.<PERSON>rrorf("Expected 401 or 404, got %d", rr.Code)
		}
	})
}
