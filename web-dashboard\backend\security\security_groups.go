package security

import (
	"database/sql"
	"fmt"

	"web-dashboard/backend/database"
	"web-dashboard/backend/debug"
)

type SecurityGroup struct {
	ID          int    `json:"id"`
	Name        string `json:"name"`
	Description string `json:"description"`
	CreatedAt   string `json:"created_at"`
	UpdatedAt   string `json:"updated_at"`
}

func GetAllSecurityGroups() ([]SecurityGroup, error) {
	query := `SELECT id, name, description, created_at, updated_at FROM security_groups ORDER BY name`
	rows, err := database.DB.Query(query)
	if err != nil {
		debug.Error("Failed to query security groups: %v", err)
		return nil, fmt.Errorf("failed to query security groups: %v", err)
	}
	defer rows.Close()

	var groups []SecurityGroup
	for rows.Next() {
		var group SecurityGroup
		if err := rows.Scan(&group.ID, &group.Name, &group.Description, &group.CreatedAt, &group.UpdatedAt); err != nil {
			debug.Error("Failed to scan security group: %v", err)
			return nil, fmt.Erro<PERSON>("failed to scan security group: %v", err)
		}
		groups = append(groups, group)
	}
	debug.Info("Successfully retrieved all security groups")
	return groups, nil
}

func CreateSecurityGroup(name, description string) (*SecurityGroup, error) {
	query := `INSERT INTO security_groups (name, description) VALUES ($1, $2) RETURNING id, name, description, created_at, updated_at`
	var group SecurityGroup
	err := database.DB.QueryRow(query, name, description).Scan(
		&group.ID, &group.Name, &group.Description, &group.CreatedAt, &group.UpdatedAt)
	if err != nil {
		debug.Error("Failed to create security group: %v", err)
		return nil, fmt.Errorf("failed to create security group: %v", err)
	}
	debug.Info("Successfully created security group: %s", name)
	return &group, nil
}

func GetSecurityGroup(id int) (*SecurityGroup, error) {
	query := `SELECT id, name, description, created_at, updated_at FROM security_groups WHERE id = $1`
	var group SecurityGroup
	err := database.DB.QueryRow(query, id).Scan(
		&group.ID, &group.Name, &group.Description, &group.CreatedAt, &group.UpdatedAt)
	if err != nil {
		if err == sql.ErrNoRows {
			debug.Info("Security group not found: %d", id)
			return nil, fmt.Errorf("security group not found")
		}
		debug.Error("Failed to get security group: %v", err)
		return nil, fmt.Errorf("failed to get security group: %v", err)
	}
	debug.Info("Successfully retrieved security group: %d", id)
	return &group, nil
}

func UpdateSecurityGroup(id int, name, description string) error {
	query := `UPDATE security_groups SET name = $1, description = $2, updated_at = CURRENT_TIMESTAMP WHERE id = $3`
	result, err := database.DB.Exec(query, name, description, id)
	if err != nil {
		debug.Error("Failed to update security group: %v", err)
		return fmt.Errorf("failed to update security group: %v", err)
	}
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		debug.Error("Failed to get rows affected: %v", err)
		return fmt.Errorf("failed to get rows affected: %v", err)
	}
	if rowsAffected == 0 {
		debug.Info("Security group not found: %d", id)
		return fmt.Errorf("security group not found")
	}
	debug.Info("Successfully updated security group: %d", id)
	return nil
}

func DeleteSecurityGroup(id int) error {
	query := `DELETE FROM security_groups WHERE id = $1`
	result, err := database.DB.Exec(query, id)
	if err != nil {
		debug.Error("Failed to delete security group: %v", err)
		return fmt.Errorf("failed to delete security group: %v", err)
	}
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		debug.Error("Failed to get rows affected: %v", err)
		return fmt.Errorf("failed to get rows affected: %v", err)
	}
	if rowsAffected == 0 {
		debug.Info("Security group not found: %d", id)
		return fmt.Errorf("security group not found")
	}
	debug.Info("Successfully deleted security group: %d", id)
	return nil
}

func GetSecurityGroupMembers(groupID int) ([]int, error) {
	query := `SELECT user_id FROM user_security_groups WHERE security_group_id = $1`
	rows, err := database.DB.Query(query, groupID)
	if err != nil {
		debug.Error("Failed to query security group members: %v", err)
		return nil, fmt.Errorf("failed to query security group members: %v", err)
	}
	defer rows.Close()

	var members []int
	for rows.Next() {
		var userID int
		if err := rows.Scan(&userID); err != nil {
			debug.Error("Failed to scan security group member: %v", err)
			return nil, fmt.Errorf("failed to scan security group member: %v", err)
		}
		members = append(members, userID)
	}
	debug.Info("Successfully retrieved security group members: %d", groupID)
	return members, nil
}

func GetSecurityGroupPermissions(groupID int) ([]Permission, error) {
	query := `
		SELECT p.id, p.name, p.description, p.resource, p.action, p.created_at, p.updated_at
		FROM permissions p
		JOIN security_group_permissions sgp ON p.id = sgp.permission_id
		WHERE sgp.security_group_id = $1
		ORDER BY p.name
	`
	rows, err := database.DB.Query(query, groupID)
	if err != nil {
		debug.Error("Failed to query security group permissions: %v", err)
		return nil, fmt.Errorf("failed to query security group permissions: %v", err)
	}
	defer rows.Close()

	var permissions []Permission
	for rows.Next() {
		var permission Permission
		if err := rows.Scan(&permission.ID, &permission.Name, &permission.Description, &permission.Resource, &permission.Action, &permission.CreatedAt, &permission.UpdatedAt); err != nil {
			debug.Error("Failed to scan permission: %v", err)
			return nil, fmt.Errorf("failed to scan permission: %v", err)
		}
		permissions = append(permissions, permission)
	}
	debug.Info("Successfully retrieved security group permissions: %d", groupID)
	return permissions, nil
}

func AddUserToSecurityGroup(userID, securityGroupID int) error {
	query := `INSERT INTO user_security_groups (user_id, security_group_id) VALUES ($1, $2)`
	_, err := database.DB.Exec(query, userID, securityGroupID)
	if err != nil {
		debug.Error("Failed to add user to security group: %v", err)
		return fmt.Errorf("failed to add user to security group: %v", err)
	}
	debug.Info("Successfully added user %d to security group %d", userID, securityGroupID)
	return nil
}

func RemoveUserFromSecurityGroup(userID, groupID int) error {
	query := `DELETE FROM user_security_groups WHERE user_id = $1 AND security_group_id = $2`
	result, err := database.DB.Exec(query, userID, groupID)
	if err != nil {
		debug.Error("Failed to remove user from security group: %v", err)
		return fmt.Errorf("failed to remove user from security group: %v", err)
	}
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		debug.Error("Failed to get rows affected: %v", err)
		return fmt.Errorf("failed to get rows affected: %v", err)
	}
	if rowsAffected == 0 {
		debug.Info("User %d not found in security group %d", userID, groupID)
		return fmt.Errorf("user not found in security group")
	}
	debug.Info("Successfully removed user %d from security group %d", userID, groupID)
	return nil
}

func AddPermissionToSecurityGroup(securityGroupID, permissionID int) error {
	query := `INSERT INTO security_group_permissions (security_group_id, permission_id) VALUES ($1, $2)`
	_, err := database.DB.Exec(query, securityGroupID, permissionID)
	if err != nil {
		debug.Error("Failed to add permission to security group: %v", err)
		return fmt.Errorf("failed to add permission to security group: %v", err)
	}
	debug.Info("Successfully added permission %d to security group %d", permissionID, securityGroupID)
	return nil
}

func RemovePermissionFromSecurityGroup(permissionID, groupID int) error {
	query := `DELETE FROM security_group_permissions WHERE permission_id = $1 AND security_group_id = $2`
	result, err := database.DB.Exec(query, permissionID, groupID)
	if err != nil {
		debug.Error("Failed to remove permission from security group: %v", err)
		return fmt.Errorf("failed to remove permission from security group: %v", err)
	}
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		debug.Error("Failed to get rows affected: %v", err)
		return fmt.Errorf("failed to get rows affected: %v", err)
	}
	if rowsAffected == 0 {
		debug.Info("Permission %d not found in security group %d", permissionID, groupID)
		return fmt.Errorf("permission not found in security group")
	}
	debug.Info("Successfully removed permission %d from security group %d", permissionID, groupID)
	return nil
}