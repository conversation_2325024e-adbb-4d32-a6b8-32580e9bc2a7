package testutils

import (
	"os"
	"path/filepath"

	"github.com/joho/godotenv"
)

// SetupTestDatabase ensures production database is NEVER accessed during tests
// This is the canonical database setup function used by all test packages
//
// CRITICAL SECURITY: This function overrides production database environment
// variables with test-specific ones. If test secrets aren't configured,
// connections will fail - this is intentional to prevent accidental
// production database access.
func SetupTestDatabase() {
	cwd, _ := os.Getwd()
	if filepath.Base(cwd) != "backend" {
		cwd = filepath.Dir(cwd)
	}
	envPath := filepath.Join(cwd, ".env")

	godotenv.Load(envPath)

	// Override production database environment variables with test values
	os.Setenv("PGHOST", os.Getenv("PGHOST_TEST"))
	os.Setenv("PGPORT", os.Getenv("PGPORT_TEST"))
	os.Setenv("PGUSER", os.Getenv("PGUSER_TEST"))
	os.Setenv("PGPASSWORD", os.<PERSON>env("PGPASSWORD_TEST"))
	os.Setenv("PGDATABASE", os.Getenv("PGDATABASE_TEST"))

	schemaPath := filepath.Join(cwd, "database", "schema.sql")
	os.Setenv("SCHEMA_PATH", schemaPath)
}

// SetupTestEnvironment sets up common test environment variables
func SetupTestEnvironment() {
	os.Setenv("TESTING", "true")
	SetupTestDatabase()
}
