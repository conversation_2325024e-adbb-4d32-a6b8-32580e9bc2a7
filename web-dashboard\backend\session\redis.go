package session

import (
	"context"
	"encoding/json"
	"fmt"
	"net"
	"net/http"
	"strings"
	"time"

	"web-dashboard/backend/database"
	"web-dashboard/backend/debug"

	"github.com/go-redis/redis/v8"
)

type Permission struct {
	ID       int    `json:"id"`
	Name     string `json:"name"`
	Resource string `json:"resource"`
	Action   string `json:"action"`
}

type User struct {
	ID    int    `json:"id"`
	Email string `json:"email"`
}

type RedisSessionManager struct {
	Client *redis.Client
	ctx    context.Context
}

type SessionData struct {
	UserID      int      `json:"user_id"`
	Email       string   `json:"email"`
	AuthType    string   `json:"auth_type"`
	Permissions []string `json:"permissions"`
	CreatedAt   int64    `json:"created_at"`
	LastAccess  int64    `json:"last_access"`
	IPAddress   string   `json:"ip_address"`
}

type IPRecord struct {
	IPAddress string `json:"ip_address"`
	Timestamp int64  `json:"timestamp"`
}

var Manager *RedisSessionManager

// GetClientIP extracts the real client IP address from the request
func GetClientIP(r *http.Request) string {
	// Check X-Forwarded-For header first (most common in production)
	if xff := r.Header.Get("X-Forwarded-For"); xff != "" {
		// X-Forwarded-For can contain multiple IPs, take the first one
		ips := strings.Split(xff, ",")
		if len(ips) > 0 {
			ip := strings.TrimSpace(ips[0])
			if net.ParseIP(ip) != nil {
				return ip
			}
		}
	}

	// Check X-Real-IP header (used by some reverse proxies)
	if xri := r.Header.Get("X-Real-IP"); xri != "" {
		if net.ParseIP(xri) != nil {
			return xri
		}
	}

	// Fall back to RemoteAddr
	ip, _, err := net.SplitHostPort(r.RemoteAddr)
	if err != nil {
		return r.RemoteAddr
	}

	return ip
}

func getUserByEmail(email string) (*User, error) {
	query := `SELECT id, email FROM users WHERE email = $1`
	var user User
	err := database.DB.QueryRow(query, email).Scan(&user.ID, &user.Email)
	if err != nil {
		return nil, fmt.Errorf("user not found: %v", err)
	}
	return &user, nil
}

func getUserPermissionsFromDB(userID int) ([]Permission, error) {
	query := `
		SELECT DISTINCT p.id, p.name, p.resource, p.action
		FROM permissions p
		JOIN security_group_permissions sgp ON p.id = sgp.permission_id
		JOIN user_security_groups usg ON sgp.security_group_id = usg.security_group_id
		WHERE usg.user_id = $1
	`

	rows, err := database.DB.Query(query, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to query user permissions: %v", err)
	}
	defer rows.Close()

	var permissions []Permission
	for rows.Next() {
		var perm Permission
		err := rows.Scan(&perm.ID, &perm.Name, &perm.Resource, &perm.Action)
		if err != nil {
			return nil, fmt.Errorf("failed to scan permission: %v", err)
		}
		permissions = append(permissions, perm)
	}

	debug.Debug("Retrieved %d permissions from database for user ID %d", len(permissions), userID)
	for _, perm := range permissions {
		debug.Debug("  - Permission: %s (Resource: %s, Action: %s)", perm.Name, perm.Resource, perm.Action)
	}

	return permissions, nil
}

func isUserInSuperAdminGroup(userID int) (bool, error) {
	query := `
		SELECT COUNT(*)
		FROM user_security_groups usg
		JOIN security_groups sg ON usg.security_group_id = sg.id
		WHERE usg.user_id = $1 AND sg.name = 'Super Admin'
	`
	var count int
	err := database.DB.QueryRow(query, userID).Scan(&count)
	if err != nil {
		return false, fmt.Errorf("failed to check Super Admin membership: %v", err)
	}
	return count > 0, nil
}

func removeDuplicates(slice []string) []string {
	keys := make(map[string]bool)
	var result []string
	for _, item := range slice {
		if !keys[item] {
			keys[item] = true
			result = append(result, item)
		}
	}
	return result
}

func InitRedis(redisURL string) error {
	opt, err := redis.ParseURL(redisURL)
	if err != nil {
		return fmt.Errorf("failed to parse Redis URL: %v", err)
	}

	// Set connection pool options for better reliability
	opt.PoolSize = 10
	opt.MinIdleConns = 1
	opt.MaxRetries = 3
	opt.DialTimeout = 5 * time.Second
	opt.ReadTimeout = 3 * time.Second
	opt.WriteTimeout = 3 * time.Second

	client := redis.NewClient(opt)
	ctx := context.Background()

	// Test connection with retries
	maxRetries := 3
	for i := 0; i < maxRetries; i++ {
		_, err = client.Ping(ctx).Result()
		if err == nil {
			break
		}
		if i < maxRetries-1 {
			debug.Warn("Redis connection attempt %d failed, retrying...", i+1)
			time.Sleep(time.Duration(i+1) * time.Second)
		}
	}

	if err != nil {
		return fmt.Errorf("failed to connect to Redis after %d attempts: %v", maxRetries, err)
	}

	Manager = &RedisSessionManager{
		Client: client,
		ctx:    ctx,
	}

	debug.Info("Redis session manager initialized successfully")
	return nil
}

func (r *RedisSessionManager) CreateSession(sessionID string, userID int, email string, authType string, permissions []string, ipAddress string) error {
	sessionData := SessionData{
		UserID:      userID,
		Email:       email,
		AuthType:    authType,
		Permissions: permissions,
		CreatedAt:   time.Now().Unix(),
		LastAccess:  time.Now().Unix(),
		IPAddress:   ipAddress,
	}

	data, err := json.Marshal(sessionData)
	if err != nil {
		return fmt.Errorf("failed to marshal session data: %v", err)
	}

	// Set session with 7 days expiration
	err = r.Client.Set(r.ctx, "session:"+sessionID, data, 7*24*time.Hour).Err()
	if err != nil {
		return fmt.Errorf("failed to store session: %v", err)
	}

	// Update IP history for the user
	if ipAddress != "" {
		err = r.UpdateUserIPHistory(userID, ipAddress)
		if err != nil {
			debug.Warn("Failed to update IP history for user %d: %v", userID, err)
			// Don't fail session creation if IP history update fails
		}
	}

	debug.Debug("Created Redis session for user %s (ID: %d) from IP %s", email, userID, ipAddress)
	return nil
}

func (r *RedisSessionManager) GetSession(sessionID string) (*SessionData, error) {
	data, err := r.Client.Get(r.ctx, "session:"+sessionID).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, fmt.Errorf("session not found")
		}
		return nil, fmt.Errorf("failed to get session: %v", err)
	}

	var sessionData SessionData
	err = json.Unmarshal([]byte(data), &sessionData)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal session data: %v", err)
	}

	// Update last access time without recursion
	sessionData.LastAccess = time.Now().Unix()

	// Update the session data in Redis directly
	updatedData, err := json.Marshal(sessionData)
	if err == nil {
		r.Client.Set(r.ctx, "session:"+sessionID, updatedData, 7*24*time.Hour)
	}

	return &sessionData, nil
}

// GetSessionReadOnly retrieves session data without updating the LastAccess timestamp
// This is used for administrative purposes like viewing session lists
func (r *RedisSessionManager) GetSessionReadOnly(sessionID string) (*SessionData, error) {
	data, err := r.Client.Get(r.ctx, "session:"+sessionID).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, fmt.Errorf("session not found")
		}
		return nil, fmt.Errorf("failed to get session: %v", err)
	}

	var sessionData SessionData
	err = json.Unmarshal([]byte(data), &sessionData)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal session data: %v", err)
	}

	// Do NOT update LastAccess - return session data as-is
	return &sessionData, nil
}

func (r *RedisSessionManager) UpdateSession(sessionID string, sessionData *SessionData) error {
	data, err := json.Marshal(sessionData)
	if err != nil {
		return fmt.Errorf("failed to marshal session data: %v", err)
	}

	err = r.Client.Set(r.ctx, "session:"+sessionID, data, 7*24*time.Hour).Err()
	if err != nil {
		return fmt.Errorf("failed to update session: %v", err)
	}

	return nil
}

func (r *RedisSessionManager) DeleteSession(sessionID string) error {
	// Check if session exists before deletion
	exists, err := r.Client.Exists(r.ctx, "session:"+sessionID).Result()
	if err != nil {
		debug.Error("Error checking session existence before deletion: %v", err)
	} else if exists == 0 {
		debug.Debug("Session %s does not exist, nothing to delete", sessionID)
		return nil
	}

	err = r.Client.Del(r.ctx, "session:"+sessionID).Err()
	if err != nil {
		return fmt.Errorf("failed to delete session: %v", err)
	}

	debug.Info("Successfully deleted Redis session: %s", sessionID)
	return nil
}

func (r *RedisSessionManager) HasPermission(sessionID string, permission string) (bool, error) {
	sessionData, err := r.GetSession(sessionID)
	if err != nil {
		debug.Error("Failed to get session %s: %v", sessionID, err)
		return false, err
	}

	debug.Debug("Checking permission '%s' for user %s (session %s)", permission, sessionData.Email, sessionID)
	debug.Trace("User permissions: %v", sessionData.Permissions)

	for _, perm := range sessionData.Permissions {
		if perm == permission || perm == "*" {
			debug.Debug("Permission '%s' granted (matched '%s')", permission, perm)
			return true, nil
		}
	}

	debug.Debug("Permission '%s' denied", permission)
	return false, nil
}

func (r *RedisSessionManager) IsSessionValid(sessionID string) bool {
	exists, err := r.Client.Exists(r.ctx, "session:"+sessionID).Result()
	if err != nil {
		debug.Error("Error checking session existence: %v", err)
		return false
	}
	return exists == 1
}

func (r *RedisSessionManager) ExtendSession(sessionID string) error {
	// Extend session expiration to 7 days from now
	err := r.Client.Expire(r.ctx, "session:"+sessionID, 7*24*time.Hour).Err()
	if err != nil {
		return fmt.Errorf("failed to extend session: %v", err)
	}
	return nil
}

func (r *RedisSessionManager) GetUserPermissions(email string) ([]string, error) {
	// Get user from database
	user, err := getUserByEmail(email)
	if err != nil {
		debug.Error("Failed to get user %s from database: %v", email, err)
		// SECURITY: Fail securely - if we can't get the user, deny access completely
		return nil, fmt.Errorf("failed to retrieve user: %v", err)
	}

	debug.Debug("Getting permissions for user ID %d (%s)", user.ID, email)

	// Check if user is in Super Admin group first
	isInSuperAdmin, err := isUserInSuperAdminGroup(user.ID)
	if err != nil {
		debug.Error("Failed to check Super Admin membership for user %s: %v", email, err)
		// SECURITY: Fail securely - if we can't verify admin status, deny admin access
		return nil, fmt.Errorf("failed to verify admin status: %v", err)
	}

	// If user is Super Admin, grant wildcard permission
	if isInSuperAdmin {
		debug.Debug("User %s is in Super Admin group, granting wildcard permission", email)
		return []string{"*"}, nil
	}

	// Get user's permissions from database
	dbPermissions, err := getUserPermissionsFromDB(user.ID)
	if err != nil {
		debug.Error("Failed to get permissions from database for user %s: %v", email, err)
		// SECURITY: Fail securely - if we can't get permissions, deny access
		return nil, fmt.Errorf("failed to retrieve permissions: %v", err)
	}

	debug.Debug("Found %d database permissions for user %s", len(dbPermissions), email)

	// Use only actual permission names from database
	sessionPermissions := []string{}

	for _, perm := range dbPermissions {
		debug.Trace("Adding permission to session: %s (Resource: %s, Action: %s)", perm.Name, perm.Resource, perm.Action)
		sessionPermissions = append(sessionPermissions, perm.Name)
	}

	// Remove duplicates
	sessionPermissions = removeDuplicates(sessionPermissions)

	debug.Debug("Final session permissions for user %s: %v", email, sessionPermissions)
	return sessionPermissions, nil
}

// GetUserIPHistory retrieves the IP history for a specific user
func (r *RedisSessionManager) GetUserIPHistory(userID int) ([]IPRecord, error) {
	key := fmt.Sprintf("user_ip_history:%d", userID)
	debug.Trace("GetUserIPHistory - Looking for Redis key: %s", key)
	
	data, err := r.Client.Get(r.ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			debug.Debug("GetUserIPHistory - No IP history found for user %d (key: %s)", userID, key)
			return []IPRecord{}, nil // No history found, return empty slice
		}
		debug.Error("GetUserIPHistory - Redis error for user %d: %v", userID, err)
		return nil, fmt.Errorf("failed to get user IP history: %v", err)
	}

	debug.Trace("GetUserIPHistory - Retrieved data for user %d: %s", userID, data)

	var ipHistory []IPRecord
	err = json.Unmarshal([]byte(data), &ipHistory)
	if err != nil {
		debug.Error("GetUserIPHistory - Failed to unmarshal data for user %d: %v", userID, err)
		return nil, fmt.Errorf("failed to unmarshal IP history: %v", err)
	}

	debug.Debug("GetUserIPHistory - Successfully retrieved %d IP records for user %d", len(ipHistory), userID)
	return ipHistory, nil
}

// UpdateUserIPHistory updates the IP history for a user with a new IP address
func (r *RedisSessionManager) UpdateUserIPHistory(userID int, ipAddress string) error {
	key := fmt.Sprintf("user_ip_history:%d", userID)
	debug.Trace("UpdateUserIPHistory - userID=%d, IP=%s, key=%s", userID, ipAddress, key)
	
	// Get existing IP history
	ipHistory, err := r.GetUserIPHistory(userID)
	if err != nil {
		debug.Error("UpdateUserIPHistory - Error getting existing history: %v", err)
		return fmt.Errorf("failed to get existing IP history: %v", err)
	}

	debug.Debug("UpdateUserIPHistory - Current history has %d records", len(ipHistory))
	currentTime := time.Now().Unix()

	// Check if IP already exists in history
	found := false
	for i := range ipHistory {
		if ipHistory[i].IPAddress == ipAddress {
			// Update timestamp for existing IP
			ipHistory[i].Timestamp = currentTime
			found = true
			debug.Debug("UpdateUserIPHistory - Updated existing IP %s timestamp to %d", ipAddress, currentTime)
			break
		}
	}

	// If IP not found, add it
	if !found {
		newRecord := IPRecord{
			IPAddress: ipAddress,
			Timestamp: currentTime,
		}
		ipHistory = append(ipHistory, newRecord)
		debug.Debug("UpdateUserIPHistory - Added new IP record: %s at %d", ipAddress, currentTime)
	}

	// Sort by timestamp (most recent first)
	for i := 0; i < len(ipHistory)-1; i++ {
		for j := i + 1; j < len(ipHistory); j++ {
			if ipHistory[i].Timestamp < ipHistory[j].Timestamp {
				ipHistory[i], ipHistory[j] = ipHistory[j], ipHistory[i]
			}
		}
	}

	// Keep only the last 5 unique IPs
	if len(ipHistory) > 5 {
		ipHistory = ipHistory[:5]
		debug.Debug("UpdateUserIPHistory - Trimmed history to 5 records")
	}

	debug.Debug("UpdateUserIPHistory - Final history has %d records", len(ipHistory))

	// Save updated history
	data, err := json.Marshal(ipHistory)
	if err != nil {
		debug.Error("UpdateUserIPHistory - Marshal error: %v", err)
		return fmt.Errorf("failed to marshal IP history: %v", err)
	}

	debug.Trace("UpdateUserIPHistory - Marshaled data: %s", string(data))

	// Store with 30 days expiration (longer than session expiration)
	err = r.Client.Set(r.ctx, key, data, 30*24*time.Hour).Err()
	if err != nil {
		debug.Error("UpdateUserIPHistory - Redis SET error: %v", err)
		return fmt.Errorf("failed to store IP history: %v", err)
	}

	debug.Debug("UpdateUserIPHistory - Successfully stored IP history for user %d", userID)
	return nil
}

// System Settings Cache Functions

const (
	systemSettingCachePrefix = "system_setting:"
	systemSettingCacheTTL    = 5 * time.Minute
)

// GetSystemSettingFromCache retrieves a cached system setting
func (r *RedisSessionManager) GetSystemSettingFromCache(key string) (string, error) {
	cacheKey := systemSettingCachePrefix + key
	value, err := r.Client.Get(r.ctx, cacheKey).Result()
	if err != nil {
		return "", err
	}
	return value, nil
}

// SetSystemSettingCache stores a system setting in cache
func (r *RedisSessionManager) SetSystemSettingCache(key, value string) error {
	cacheKey := systemSettingCachePrefix + key
	return r.Client.Set(r.ctx, cacheKey, value, systemSettingCacheTTL).Err()
}

// DeleteSystemSettingCache removes a system setting from cache
func (r *RedisSessionManager) DeleteSystemSettingCache(key string) error {
	cacheKey := systemSettingCachePrefix + key
	return r.Client.Del(r.ctx, cacheKey).Err()
}

// InvalidateAllSystemSettingsCache clears all system settings from cache
func (r *RedisSessionManager) InvalidateAllSystemSettingsCache() error {
	keys, err := r.Client.Keys(r.ctx, systemSettingCachePrefix+"*").Result()
	if err != nil {
		return err
	}
	if len(keys) > 0 {
		return r.Client.Del(r.ctx, keys...).Err()
	}
	return nil
}