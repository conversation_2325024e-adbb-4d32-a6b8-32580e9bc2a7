package users

import (
	"crypto/rand"
	"database/sql"
	"fmt"
	"net/http"
	"time"

	"web-dashboard/backend/database"
	"web-dashboard/backend/debug"
	"web-dashboard/backend/security"
	"web-dashboard/backend/session"

	"golang.org/x/crypto/argon2"
)

// User represents a user in the system
type User struct {
	ID              int       `json:"id"`
	Email           string    `json:"email"`
	PasswordHash    string    `json:"-"`
	Enabled         bool      `json:"enabled"`
	LastLoginMethod string    `json:"last_login_method"`
	LastLoginAt     time.Time `json:"last_login_at"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
}

// GetUserByID retrieves a user by their ID
func GetUserByID(id int) (*User, error) {
	query := `SELECT id, email, enabled, last_login_method, last_login_at, created_at, updated_at FROM users WHERE id = $1`

	var user User
	var lastLoginAt sql.NullTime

	err := database.DB.QueryRow(query, id).Scan(
		&user.ID, &user.Email, &user.Enabled, &user.LastLoginMethod,
		&lastLoginAt, &user.CreatedAt, &user.UpdatedAt)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("user not found")
		}
		return nil, fmt.Errorf("failed to get user: %v", err)
	}

	if lastLoginAt.Valid {
		user.LastLoginAt = lastLoginAt.Time
	}

	return &user, nil
}

// GetUserByEmail retrieves a user by their email address (without password hash for security)
func GetUserByEmail(email string) (*User, error) {
	var user User
	var lastLoginAt sql.NullTime
	var lastLoginMethod sql.NullString

	// Use a single QueryRow call with explicit column selection and proper null handling
	query := `SELECT id, email, enabled, 
	                 COALESCE(last_login_method, '') as last_login_method,
	                 last_login_at, created_at, updated_at 
	          FROM users WHERE email = $1`

	err := database.DB.QueryRow(query, email).Scan(
		&user.ID,
		&user.Email,
		&user.Enabled,
		&lastLoginMethod,
		&lastLoginAt,
		&user.CreatedAt,
		&user.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("user not found")
		}
		return nil, fmt.Errorf("failed to get user: %v", err)
	}

	// Handle nullable fields
	if lastLoginMethod.Valid {
		user.LastLoginMethod = lastLoginMethod.String
	}

	if lastLoginAt.Valid {
		user.LastLoginAt = lastLoginAt.Time
	}

	return &user, nil
}

// GetUserByEmailWithPassword retrieves a user by email including password hash (for authentication only)
func GetUserByEmailWithPassword(email string) (*User, error) {
	query := `SELECT id, email, password_hash, enabled, last_login_method, last_login_at, created_at, updated_at FROM users WHERE email = $1`

	var user User
	var lastLoginAt sql.NullTime

	err := database.DB.QueryRow(query, email).Scan(
		&user.ID, &user.Email, &user.PasswordHash, &user.Enabled, &user.LastLoginMethod,
		&lastLoginAt, &user.CreatedAt, &user.UpdatedAt)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("user not found")
		}
		return nil, fmt.Errorf("failed to get user: %v", err)
	}

	if lastLoginAt.Valid {
		user.LastLoginAt = lastLoginAt.Time
	}

	return &user, nil
}

// GetAllUsers retrieves all users from the database
func GetAllUsers() ([]User, error) {
	query := `SELECT id, email, enabled, last_login_method, last_login_at, created_at, updated_at FROM users ORDER BY email`
	rows, err := database.DB.Query(query)
	if err != nil {
		return nil, fmt.Errorf("failed to query users: %v", err)
	}
	defer rows.Close()

	var userList []User
	for rows.Next() {
		var user User
		var lastLoginAt sql.NullTime

		err := rows.Scan(&user.ID, &user.Email, &user.Enabled, &user.LastLoginMethod,
			&lastLoginAt, &user.CreatedAt, &user.UpdatedAt)
		if err != nil {
			return nil, fmt.Errorf("failed to scan user: %v", err)
		}

		if lastLoginAt.Valid {
			user.LastLoginAt = lastLoginAt.Time
		}

		userList = append(userList, user)
	}

	return userList, nil
}

// generateComplexPassword generates a complex password for OAuth users
func generateComplexPassword() string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*"
	const length = 32

	password := make([]byte, length)
	for i := range password {
		b := make([]byte, 1)
		rand.Read(b)
		password[i] = charset[b[0]%byte(len(charset))]
	}

	return string(password)
}

// hashPassword creates a hash of the password using Argon2
func hashPassword(password string) (string, error) {
	// Generate a random salt
	salt := make([]byte, 16)
	if _, err := rand.Read(salt); err != nil {
		return "", err
	}

	// Hash the password
	hash := argon2.IDKey([]byte(password), salt, 1, 64*1024, 4, 32)

	// Encode the salt and hash
	return fmt.Sprintf("%x:%x", salt, hash), nil
}

// CreateUser creates a new user in the database
func CreateUser(email, unhashedPassword string, sendNotification bool, enabled bool) (int, error) {
	hashedPassword, err := hashPassword(unhashedPassword)

	if err != nil {
		return 0, fmt.Errorf("failed to hash password: %v", err)
	}
	// Check if user already exists
	existingUser, err := GetUserByEmail(email)

	if err != nil && err.Error() != "user not found" {
		return 0, fmt.Errorf("database error: %v", err)
	}
	if existingUser != nil {
		return 0, fmt.Errorf("user with email %s already exists", email)
	}
	// Insert new user into the database

	var userID int
	err = database.DB.QueryRow("INSERT INTO users (email, password_hash, enabled) VALUES ($1, $2, $3) RETURNING id", email, hashedPassword, enabled).Scan(&userID)
	if err != nil {
		return 0, err
	}

	return userID, nil
}

// CreateOrUpdateUser creates a new user or updates an existing one
func CreateOrUpdateUser(email, loginMethod string) (*User, error) {
	// First try to get existing user
	user, err := GetUserByEmail(email)
	if err != nil && err.Error() != "user not found" {
		return nil, fmt.Errorf("database error: %v", err)
	}

	// If user exists, update login method and return
	if user != nil {
		// Update last login method and time
		query := `UPDATE users SET last_login_method = $1, last_login_at = $2, updated_at = $3 WHERE email = $4`
		_, err = database.DB.Exec(query, loginMethod, time.Now(), time.Now(), email)
		if err != nil {
			debug.Warn("Failed to update user login info: %v", err)
		}
		return user, nil
	}

	// Generate a complex password for OAuth users
	complexPassword := generateComplexPassword()
	hashedPassword, err := hashPassword(complexPassword)
	if err != nil {
		return nil, fmt.Errorf("failed to hash password: %v", err)
	}

	// Create new user
	query := `INSERT INTO users (email, password_hash, enabled, last_login_method, last_login_at, created_at, updated_at) 
              VALUES ($1, $2, true, $3, $4, $5, $6) RETURNING id`

	var userID int
	now := time.Now()
	err = database.DB.QueryRow(query, email, hashedPassword, loginMethod, now, now, now).Scan(&userID)
	if err != nil {
		debug.Error("Failed to create user %s: %v", email, err)
		return nil, fmt.Errorf("failed to create user: %v", err)
	}

	// Add new user to default User security group (security group ID 2 is "User" security group)
	err = security.AddUserToSecurityGroup(userID, 2)
	if err != nil {
		debug.Warn("Failed to add new user %s to User security group: %v", email, err)
	}

	// Return the newly created user
	newUser, err := GetUserByID(userID)
	if err != nil {
		return nil, err
	}

	debug.Debug("Successfully created user: %s", newUser.Email)

	return newUser, nil
}

// GetCurrentUser returns the full user object for the current authenticated user
func GetCurrentUser(r *http.Request) (*User, error) {
	userEmail, err := GetCurrentUserEmail(r)
	if err != nil {
		return nil, err
	}

	user, err := GetUserByEmail(userEmail)
	if err != nil {
		return nil, fmt.Errorf("failed to get user data: %v", err)
	}

	return user, nil
}

// GetCurrentUserEmail returns the email of the current authenticated user
func GetCurrentUserEmail(r *http.Request) (string, error) {
	// Get session token from cookie
	cookie, err := r.Cookie("session_token")
	if err != nil {
		return "", fmt.Errorf("user not authenticated")
	}

	// Get session from Redis - we need to import session package
	sessionData, err := session.Manager.GetSession(cookie.Value)
	if err != nil {
		return "", fmt.Errorf("user not authenticated")
	}

	return sessionData.Email, nil
}

// EnableUser enables a user by ID
func EnableUser(userID int) error {
	query := `UPDATE users SET enabled = true, updated_at = $1 WHERE id = $2`
	result, err := database.DB.Exec(query, time.Now(), userID)
	if err != nil {
		return fmt.Errorf("failed to enable user: %v", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %v", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("user not found")
	}

	return nil
}

// DisableUser disables a user by ID
func DisableUser(userID int) error {
	query := `UPDATE users SET enabled = false, updated_at = $1 WHERE id = $2`
	result, err := database.DB.Exec(query, time.Now(), userID)
	if err != nil {
		return fmt.Errorf("failed to disable user: %v", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %v", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("user not found")
	}

	return nil
}

// DeleteUser deletes a user by ID
func DeleteUser(userID int) error {
	query := `DELETE FROM users WHERE id = $1`
	result, err := database.DB.Exec(query, userID)
	if err != nil {
		return fmt.Errorf("failed to delete user: %v", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %v", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("user not found")
	}

	return nil
}

// getSuperAdminEmails gets all super admin email addresses
func GetSuperAdminEmails() ([]string, error) {
	query := `
		SELECT DISTINCT u.email 
		FROM users u
		JOIN user_security_groups usg ON u.id = usg.user_id
		JOIN security_groups sg ON usg.security_group_id = sg.id
		WHERE sg.name = 'Super Admin' AND u.enabled = true
	`

	rows, err := database.DB.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var emails []string
	for rows.Next() {
		var email string
		if err := rows.Scan(&email); err != nil {
			continue
		}
		emails = append(emails, email)
	}

	return emails, nil
}