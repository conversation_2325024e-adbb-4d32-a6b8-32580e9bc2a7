package api

import (
	"encoding/json"
	"net/http"
	"strconv"
	"time"

	"web-dashboard/backend/auth"
	"web-dashboard/backend/database"
	"web-dashboard/backend/security"
	"web-dashboard/backend/users"

	"github.com/gorilla/mux"
)

type CreateUserRequest struct {
	Email    string `json:"email"`
	Password string `json:"password"`
}

type UpdateUserRequest struct {
	Email   string `json:"email"`
	Enabled bool   `json:"enabled"`
}

type ChangePasswordRequest struct {
	CurrentPassword string `json:"current_password"`
	NewPassword     string `json:"new_password"`
}

// AttachUserRoutes attaches all user-related routes to the router
func AttachUserRoutes(router *mux.Router) {
	router.HandleFunc("/users", RequireUserManagement(createUserHandler)).Methods("POST")
	router.HandleFunc("/users/{id:[0-9]+}", RequireUserManagement(getUserHandler)).Methods("GET")
	router.HandleFunc("/users/{id:[0-9]+}", RequireUserManagement(updateUserHandler)).Methods("PUT")
	router.HandleFunc("/users/{userID:[0-9]+}/permissions", RequirePermissionManagement(getUserPermissionsHandler)).Methods("GET")
	router.HandleFunc("/users/{userID:[0-9]+}/groups", RequirePermissionManagement(getUserGroupsHandler)).Methods("GET")
	router.HandleFunc("/users/{userID:[0-9]+}/check-permission", RequirePermissionManagement(checkUserPermissionHandler)).Methods("POST")
	router.HandleFunc("/users/me", RequireUserProfilePermission(auth.UserInfoHandler)).Methods("GET")
	router.HandleFunc("/users/me/change-password", RequireUserProfilePermission(changePasswordHandler)).Methods("POST")
}

func createUserHandler(w http.ResponseWriter, r *http.Request) {
	var req CreateUserRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	if req.Email == "" || req.Password == "" {
		http.Error(w, "Email and password are required", http.StatusBadRequest)
		return
	}

	// Create user with local authentication
	userID, err := users.CreateUser(req.Email, req.Password, true, false)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	// Get the created user
	user, err := users.GetUserByID(userID)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusCreated)
	json.NewEncoder(w).Encode(user)
}

func getUserHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	userID, err := strconv.Atoi(vars["id"])
	if err != nil {
		http.Error(w, "Invalid user ID", http.StatusBadRequest)
		return
	}

	user, err := users.GetUserByID(userID)
	if err != nil {
		http.Error(w, err.Error(), http.StatusNotFound)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(user)
}

func updateUserHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	userID, err := strconv.Atoi(vars["id"])
	if err != nil {
		http.Error(w, "Invalid user ID", http.StatusBadRequest)
		return
	}

	var req UpdateUserRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	// Update user fields in database
	query := `UPDATE users SET email = $1, enabled = $2, updated_at = $3 WHERE id = $4`
	result, err := database.DB.Exec(query, req.Email, req.Enabled, time.Now(), userID)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	// Check if user was found and updated
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}
	if rowsAffected == 0 {
		http.Error(w, "User not found", http.StatusNotFound)
		return
	}

	w.WriteHeader(http.StatusOK)
}

func changePasswordHandler(w http.ResponseWriter, r *http.Request) {
	// This can delegate to the existing ChangePasswordHandler in auth
	auth.ChangePasswordHandler(w, r)
}

func getUserPermissionsHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	userID, err := strconv.Atoi(vars["userID"])
	if err != nil {
		http.Error(w, "Invalid user ID", http.StatusBadRequest)
		return
	}

	permissions, err := security.GetUserPermissions(userID)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(permissions)
}

func getUserGroupsHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	userID, err := strconv.Atoi(vars["userID"])
	if err != nil {
		http.Error(w, "Invalid user ID", http.StatusBadRequest)
		return
	}

	userSecurityGroups, err := security.GetUserSecurityGroups(userID)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(userSecurityGroups)
}

func checkUserPermissionHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	userID, err := strconv.Atoi(vars["userID"])
	if err != nil {
		http.Error(w, "Invalid user ID", http.StatusBadRequest)
		return
	}

	var req CheckPermissionRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	hasPermission, err := security.UserHasPermission(userID, req.Resource, req.Action)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	response := CheckPermissionResponse{
		HasPermission: hasPermission,
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}