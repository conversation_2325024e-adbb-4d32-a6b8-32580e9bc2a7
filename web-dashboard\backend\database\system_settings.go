package database

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"web-dashboard/backend/config"
	"web-dashboard/backend/debug"
)

type SystemSetting struct {
	ID          int       `json:"id"`
	Key         string    `json:"key"`
	Value       string    `json:"value"`
	DataType    string    `json:"data_type"`
	Description string    `json:"description"`
	Category    string    `json:"category"`
	IsEncrypted bool      `json:"is_encrypted"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// CacheManager interface for Redis operations to avoid circular imports
type CacheManager interface {
	GetSystemSettingFromCache(key string) (string, error)
	SetSystemSettingCache(key, value string) error
	DeleteSystemSettingCache(key string) error
}

var cacheManager CacheManager

// SetCacheManager sets the cache manager for system settings
func SetCacheManager(cm CacheManager) {
	cacheManager = cm
	// Initialize debug config with cache manager and database fetcher
	config.InitDebugConfig(cm, fetchSystemSettingFromDB)
}

// fetchSystemSettingFromDB is a callback function for the config package
func fetchSystemSettingFromDB(key string) (string, error) {
	setting, err := getSystemSettingFromDB(key)
	if err != nil {
		return "", err
	}

	return convertSystemSettingToString(setting)
}

// getSystemSettingFromDB retrieves a system setting directly from database (bypassing cache)
func getSystemSettingFromDB(key string) (*SystemSetting, error) {
	query := `SELECT id, setting_key, setting_value, data_type, description, category, is_encrypted, created_at, updated_at 
			  FROM system_settings WHERE setting_key = $1`

	var setting SystemSetting
	err := DB.QueryRow(query, key).Scan(
		&setting.ID, &setting.Key, &setting.Value, &setting.DataType,
		&setting.Description, &setting.Category, &setting.IsEncrypted,
		&setting.CreatedAt, &setting.UpdatedAt,
	)

	if err != nil {
		return nil, err
	}

	return &setting, nil
}

// GetSystemSetting retrieves a single system setting by key
func GetSystemSetting(key string) (*SystemSetting, error) {
	// Try Redis cache first if cache manager is available
	if cacheManager != nil {
		if cacheValue, err := cacheManager.GetSystemSettingFromCache(key); err == nil {
			debug.Trace("System setting '%s' found in cache", key)
			return convertStringToSystemSetting(cacheValue)
		}
	}

	// Cache miss - get from database
	query := `SELECT id, setting_key, setting_value, data_type, description, category, is_encrypted, created_at, updated_at 
			  FROM system_settings WHERE setting_key = $1`

	var setting SystemSetting
	err := DB.QueryRow(query, key).Scan(
		&setting.ID, &setting.Key, &setting.Value, &setting.DataType,
		&setting.Description, &setting.Category, &setting.IsEncrypted,
		&setting.CreatedAt, &setting.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			debug.Debug("setting not found")
			return nil, fmt.Errorf("setting not found")
		}
		debug.Error("failed to get setting: %v", err)
		return nil, fmt.Errorf("failed to get setting: %v", err)
	}

	// Cache the result if cache manager is available
	if cacheManager != nil {
		cacheString, err := convertSystemSettingToString(&setting)
		if err != nil {
			debug.Warn("Failed to convert system setting to string: %v", err)
		} else {
			if err := cacheManager.SetSystemSettingCache(key, cacheString); err != nil {
				debug.Warn("Failed to cache system setting %s: %v", key, err)
				// Don't fail the request if caching fails
			}
		}
	}

	debug.Debug("System setting '%s' retrieved from database and cached", key)
	return &setting, nil
}

// GetAllSystemSettings retrieves all system settings
func GetAllSystemSettings() ([]SystemSetting, error) {
	query := `SELECT id, setting_key, setting_value, data_type, description, category, is_encrypted, created_at, updated_at 
			  FROM system_settings ORDER BY category, setting_key`

	rows, err := DB.Query(query)
	if err != nil {
		debug.Error("failed to query settings: %v", err)
		return nil, fmt.Errorf("failed to query settings: %v", err)
	}
	defer rows.Close()

	var settings []SystemSetting
	for rows.Next() {
		var setting SystemSetting
		err := rows.Scan(
			&setting.ID, &setting.Key, &setting.Value, &setting.DataType,
			&setting.Description, &setting.Category, &setting.IsEncrypted,
			&setting.CreatedAt, &setting.UpdatedAt,
		)
		if err != nil {
			debug.Error("failed to scan setting: %v", err)
			return nil, fmt.Errorf("failed to scan setting: %v", err)
		}
		settings = append(settings, setting)
	}

	return settings, nil
}

// GetSystemSettingsByCategory retrieves settings by category
func GetSystemSettingsByCategory(category string) ([]SystemSetting, error) {
	query := `SELECT id, setting_key, setting_value, data_type, description, category, is_encrypted, created_at, updated_at 
			  FROM system_settings WHERE category = $1 ORDER BY setting_key`

	rows, err := DB.Query(query, category)
	if err != nil {
		debug.Error("failed to query settings by category: %v", err)
		return nil, fmt.Errorf("failed to query settings by category: %v", err)
	}
	defer rows.Close()

	var settings []SystemSetting
	for rows.Next() {
		var setting SystemSetting
		err := rows.Scan(
			&setting.ID, &setting.Key, &setting.Value, &setting.DataType,
			&setting.Description, &setting.Category, &setting.IsEncrypted,
			&setting.CreatedAt, &setting.UpdatedAt,
		)
		if err != nil {
			debug.Error("failed to scan setting: %v", err)
			return nil, fmt.Errorf("failed to scan setting: %v", err)
		}
		settings = append(settings, setting)
	}

	return settings, nil
}

// SetSystemSetting creates or updates a system setting
func SetSystemSetting(key, value, dataType, description, category string, isEncrypted bool) error {
	query := `INSERT INTO system_settings (setting_key, setting_value, data_type, description, category, is_encrypted, updated_at) 
			  VALUES ($1, $2, $3, $4, $5, $6, $7)
			  ON CONFLICT (setting_key) 
			  DO UPDATE SET 
				setting_value = EXCLUDED.setting_value,
				data_type = EXCLUDED.data_type,
				description = EXCLUDED.description,
				category = EXCLUDED.category,
				is_encrypted = EXCLUDED.is_encrypted,
				updated_at = EXCLUDED.updated_at`

	_, err := DB.Exec(query, key, value, dataType, description, category, isEncrypted, time.Now())
	if err != nil {
		debug.Error("failed to set setting: %v", err)
		return fmt.Errorf("failed to set setting: %v", err)
	}

	// Invalidate cache entry if cache manager is available
	if cacheManager != nil {
		if err := cacheManager.DeleteSystemSettingCache(key); err != nil {
			debug.Warn("Failed to invalidate cache for system setting %s: %v", key, err)
			// Don't fail the request if cache invalidation fails
		}
	}

	return nil
}

// DeleteSystemSetting removes a system setting
func DeleteSystemSetting(key string) error {
	query := `DELETE FROM system_settings WHERE setting_key = $1`
	result, err := DB.Exec(query, key)
	if err != nil {
		debug.Error("failed to delete setting: %v", err)
		return fmt.Errorf("failed to delete setting: %v", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		debug.Error("failed to get rows affected: %v", err)
		return fmt.Errorf("failed to get rows affected: %v", err)
	}

	if rowsAffected == 0 {
		debug.Debug("setting not found")
		return fmt.Errorf("setting not found")
	}

	return nil
}

// GetSettingValue retrieves and converts a setting value to the appropriate type
func GetSettingValue(key string) (interface{}, error) {
	setting, err := GetSystemSetting(key)
	if err != nil {
		return nil, err
	}

	switch setting.DataType {
	case "string":
		return setting.Value, nil
	case "integer":
		return strconv.Atoi(setting.Value)
	case "boolean":
		return strconv.ParseBool(setting.Value)
	case "float":
		return strconv.ParseFloat(setting.Value, 64)
	case "json":
		var result interface{}
		err := json.Unmarshal([]byte(setting.Value), &result)
		return result, err
	default:
		return setting.Value, nil
	}
}

// GetSettingString is a convenience function for string settings
func GetSettingString(key, defaultValue string) string {
	if value, err := GetSettingValue(key); err == nil {
		if str, ok := value.(string); ok {
			return str
		}
	}
	return defaultValue
}

// GetSettingInt is a convenience function for integer settings
func GetSettingInt(key string, defaultValue int) int {
	if value, err := GetSettingValue(key); err == nil {
		if intVal, ok := value.(int); ok {
			return intVal
		}
	}
	return defaultValue
}

// GetSettingBool is a convenience function for boolean settings
func GetSettingBool(key string, defaultValue bool) bool {
	if value, err := GetSettingValue(key); err == nil {
		if boolVal, ok := value.(bool); ok {
			return boolVal
		}
	}
	return defaultValue
}

func convertSystemSettingToString(setting *SystemSetting) (string, error) {
	settingBytes, err := json.Marshal(setting)
	if err != nil {
		return "", fmt.Errorf("failed to marshal system setting: %v", err)
	}
	return string(settingBytes), nil
}

func convertStringToSystemSetting(settingString string) (*SystemSetting, error) {
	var setting SystemSetting
	err := json.Unmarshal([]byte(settingString), &setting)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal system setting: %v", err)
	}
	return &setting, nil
}
